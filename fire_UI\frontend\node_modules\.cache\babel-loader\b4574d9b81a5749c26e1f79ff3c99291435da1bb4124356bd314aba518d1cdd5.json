{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\Assets.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AssetForm = ({\n  asset,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: (asset === null || asset === void 0 ? void 0 : asset.name) || '',\n    value: (asset === null || asset === void 0 ? void 0 : asset.value) || 0,\n    annual_interest: (asset === null || asset === void 0 ? void 0 : asset.annual_interest) || null,\n    notes: (asset === null || asset === void 0 ? void 0 : asset.notes) || '',\n    categories: (asset === null || asset === void 0 ? void 0 : asset.categories) || [{\n      name: 'Bourse',\n      value: 0\n    }],\n    update_date: asset !== null && asset !== void 0 && asset.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n  });\n  const handleCategoryChange = (index, field, value) => {\n    const newCategories = [...formData.categories];\n    newCategories[index] = {\n      ...newCategories[index],\n      [field]: value\n    };\n    setFormData({\n      ...formData,\n      categories: newCategories\n    });\n  };\n  const addCategory = () => {\n    setFormData({\n      ...formData,\n      categories: [...formData.categories, {\n        name: 'Bourse',\n        value: 0\n      }]\n    });\n  };\n  const removeCategory = index => {\n    const newCategories = [...formData.categories];\n    newCategories.splice(index, 1);\n    setFormData({\n      ...formData,\n      categories: newCategories\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const method = asset !== null && asset !== void 0 && asset.id ? 'put' : 'post';\n    const url = asset !== null && asset !== void 0 && asset.id ? `/assets/${asset.id}` : '/assets';\n    try {\n      await apiClient[method](url, formData);\n      onSave();\n    } catch (error) {\n      console.error(\"Erreur lors de la sauvegarde de l'actif\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Nom\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        value: formData.name,\n        onChange: e => setFormData({\n          ...formData,\n          name: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Valeur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.value,\n        onChange: e => setFormData({\n          ...formData,\n          value: parseFloat(e.target.value)\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Int\\xE9r\\xEAt Annuel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.annual_interest || '',\n        onChange: e => setFormData({\n          ...formData,\n          annual_interest: parseFloat(e.target.value) || null\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Notes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        value: formData.notes || '',\n        onChange: e => setFormData({\n          ...formData,\n          notes: e.target.value\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n      children: \"Cat\\xE9gories\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this), formData.categories.map((category, index) => /*#__PURE__*/_jsxDEV(InputGroup, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Select, {\n        value: category.name,\n        onChange: e => handleCategoryChange(index, 'name', e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Bourse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Immobilier\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Crypto-actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Pr\\xEAts Participatifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Fonds S\\xE9curis\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Liquidit\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        type: \"number\",\n        value: category.value,\n        onChange: e => handleCategoryChange(index, 'value', parseFloat(e.target.value))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-danger\",\n        onClick: () => removeCategory(index),\n        children: \"X\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 21\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 17\n    }, this)), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outline-primary\",\n      onClick: addCategory,\n      children: \"Ajouter une cat\\xE9gorie\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Date de mise \\xE0 jour\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"date\",\n        value: formData.update_date,\n        onChange: e => setFormData({\n          ...formData,\n          update_date: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"primary\",\n      type: \"submit\",\n      children: \"Sauvegarder\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"secondary\",\n      onClick: onCancel,\n      className: \"ms-2\",\n      children: \"Annuler\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 9\n  }, this);\n};\n_s(AssetForm, \"gw+FoCmpjccrmczZJIIY+TCRelM=\");\n_c = AssetForm;\nconst Assets = () => {\n  _s2();\n  const [assets, setAssets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedAsset, setSelectedAsset] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchAssets = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/assets').then(response => {\n      setAssets(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('Assets API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchAssets(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des actifs.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchAssets();\n  }, []);\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedAsset(null);\n    fetchAssets();\n  };\n  const handleDelete = async id => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet actif ?\")) {\n      try {\n        await apiClient.delete(`/assets/${id}`);\n        fetchAssets();\n      } catch (error) {\n        console.error(\"Erreur lors de la suppression de l'actif\", error);\n      }\n    }\n  };\n  if (loading && !showModal) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 37\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchAssets,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Mon Patrimoine (Actifs)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => {\n          setSelectedAsset({});\n          setShowModal(true);\n        },\n        children: \"Ajouter un Actif\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table table-striped table-hover\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-dark\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Nom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Int\\xE9r\\xEAt Annuel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Valeur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Derni\\xE8re M\\xE0J\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: assets.map(asset => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: asset.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: asset.categories.map(c => `${c.name} (${new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(c.value)})`).join(', ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: asset.annual_interest ? `${asset.annual_interest}%` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(asset.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: new Date(asset.update_date).toLocaleDateString('fr-FR')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => {\n                setSelectedAsset(asset);\n                setShowModal(true);\n              },\n              children: \"Modifier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), ' ', /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: () => handleDelete(asset.id),\n              children: \"Supprimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, asset.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [selectedAsset !== null && selectedAsset !== void 0 && selectedAsset.id ? 'Modifier' : 'Ajouter', \" un Actif\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(AssetForm, {\n          asset: selectedAsset,\n          onSave: handleSave,\n          onCancel: () => setShowModal(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s2(Assets, \"eh7lWDQ0Yxh7bCt8el8bsd7dr44=\");\n_c2 = Assets;\nexport default Assets;\nvar _c, _c2;\n$RefreshReg$(_c, \"AssetForm\");\n$RefreshReg$(_c2, \"Assets\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Modal", "<PERSON><PERSON>", "Form", "InputGroup", "FormControl", "jsxDEV", "_jsxDEV", "AssetForm", "asset", "onSave", "onCancel", "_s", "formData", "setFormData", "name", "value", "annual_interest", "notes", "categories", "update_date", "Date", "toISOString", "split", "handleCategoryChange", "index", "field", "newCategories", "addCategory", "removeCategory", "splice", "handleSubmit", "e", "preventDefault", "method", "id", "url", "error", "console", "onSubmit", "children", "Group", "className", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "type", "onChange", "target", "required", "parseFloat", "map", "category", "Select", "variant", "onClick", "_c", "Assets", "_s2", "assets", "setAssets", "loading", "setLoading", "setError", "showModal", "setShowModal", "selectedAsset", "setSelectedAsset", "retryCount", "setRetryCount", "fetchAssets", "get", "then", "response", "data", "catch", "prev", "setTimeout", "handleSave", "handleDelete", "window", "confirm", "delete", "c", "Intl", "NumberFormat", "style", "currency", "format", "join", "toLocaleDateString", "size", "show", "onHide", "Header", "closeButton", "Title", "Body", "_c2", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/Assets.tsx"], "sourcesContent": ["import React, { useEffect, useState, FormEvent } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';\n\ninterface CategoryValue {\n  name: string;\n  value: number;\n}\n\ninterface ApiCategoryValue {\n  category_name: string;\n  value: number;\n}\n\ninterface Asset {\n  id: number;\n  name: string;\n  value: number;\n  annual_interest: number | null;\n  notes: string | null;\n  update_date: string;\n  categories: ApiCategoryValue[];\n  liabilities: any[];\n}\n\nconst AssetForm: React.FC<{ asset: Partial<Asset> | null, onSave: () => void, onCancel: () => void }> = ({ asset, onSave, onCancel }) => {\n    const [formData, setFormData] = useState({\n        name: asset?.name || '',\n        value: asset?.value || 0,\n        annual_interest: asset?.annual_interest || null,\n        notes: asset?.notes || '',\n        categories: asset?.categories || [{ name: 'Bourse', value: 0 }],\n        update_date: asset?.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n    });\n\n    const handleCategoryChange = (index: number, field: string, value: any) => {\n        const newCategories = [...formData.categories];\n        newCategories[index] = { ...newCategories[index], [field]: value };\n        setFormData({ ...formData, categories: newCategories });\n    };\n\n    const addCategory = () => {\n        setFormData({ ...formData, categories: [...formData.categories, { name: 'Bourse', value: 0 }] });\n    };\n\n    const removeCategory = (index: number) => {\n        const newCategories = [...formData.categories];\n        newCategories.splice(index, 1);\n        setFormData({ ...formData, categories: newCategories });\n    };\n\n    const handleSubmit = async (e: FormEvent) => {\n        e.preventDefault();\n        const method = asset?.id ? 'put' : 'post';\n        const url = asset?.id ? `/assets/${asset.id}` : '/assets';\n        \n        try {\n            await apiClient[method](url, formData);\n            onSave();\n        } catch (error) {\n            console.error(\"Erreur lors de la sauvegarde de l'actif\", error);\n        }\n    };\n\n    return (\n        <Form onSubmit={handleSubmit}>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Nom</Form.Label>\n                <Form.Control type=\"text\" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Valeur</Form.Label>\n                <Form.Control type=\"number\" value={formData.value} onChange={e => setFormData({ ...formData, value: parseFloat(e.target.value) })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Intérêt Annuel</Form.Label>\n                <Form.Control type=\"number\" value={formData.annual_interest || ''} onChange={e => setFormData({ ...formData, annual_interest: parseFloat(e.target.value) || null })} />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Notes</Form.Label>\n                <Form.Control type=\"text\" value={formData.notes || ''} onChange={e => setFormData({ ...formData, notes: e.target.value })} />\n            </Form.Group>\n            \n            <h5>Catégories</h5>\n            {formData.categories.map((category, index) => (\n                <InputGroup className=\"mb-3\" key={index}>\n                    <Form.Select value={category.name} onChange={e => handleCategoryChange(index, 'name', e.target.value)}>\n                        <option>Bourse</option>\n                        <option>Immobilier</option>\n                        <option>Crypto-actifs</option>\n                        <option>Prêts Participatifs</option>\n                        <option>Fonds Sécurisés</option>\n                        <option>Liquidités</option>\n                    </Form.Select>\n                    <FormControl type=\"number\" value={category.value} onChange={e => handleCategoryChange(index, 'value', parseFloat(e.target.value))} />\n                    <Button variant=\"outline-danger\" onClick={() => removeCategory(index)}>X</Button>\n                </InputGroup>\n            ))}\n            <Button variant=\"outline-primary\" onClick={addCategory}>Ajouter une catégorie</Button>\n\n            <Form.Group className=\"mb-3 mt-3\">\n                <Form.Label>Date de mise à jour</Form.Label>\n                <Form.Control type=\"date\" value={formData.update_date} onChange={e => setFormData({ ...formData, update_date: e.target.value })} required />\n            </Form.Group>\n            <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\n            <Button variant=\"secondary\" onClick={onCancel} className=\"ms-2\">Annuler</Button>\n        </Form>\n    );\n};\n\n\nconst Assets: React.FC = () => {\n  const [assets, setAssets] = useState<Asset[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedAsset, setSelectedAsset] = useState<Partial<Asset> | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchAssets = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/assets')\n      .then(response => {\n        setAssets(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('Assets API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchAssets(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des actifs.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchAssets();\n  }, []);\n\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedAsset(null);\n    fetchAssets();\n  };\n\n  const handleDelete = async (id: number) => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet actif ?\")) {\n        try {\n            await apiClient.delete(`/assets/${id}`);\n            fetchAssets();\n        } catch (error) {\n            console.error(\"Erreur lors de la suppression de l'actif\", error);\n        }\n    }\n  };\n\n  if (loading && !showModal) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchAssets}>\n        Réessayer\n      </button>\n    </div>\n  );\n\n  return (\n    <div>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1>Mon Patrimoine (Actifs)</h1>\n        <Button variant=\"primary\" onClick={() => { setSelectedAsset({}); setShowModal(true); }}>Ajouter un Actif</Button>\n      </div>\n      <table className=\"table table-striped table-hover\">\n        <thead className=\"table-dark\">\n          <tr>\n            <th>Nom</th>\n            <th>Catégories</th>\n            <th className=\"text-end\">Intérêt Annuel</th>\n            <th className=\"text-end\">Valeur</th>\n            <th className=\"text-center\">Dernière MàJ</th>\n            <th className=\"text-center\">Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          {assets.map(asset => (\n            <tr key={asset.id}>\n              <td>{asset.name}</td>\n              <td>{asset.categories.map(c => `${c.name} (${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(c.value)})`).join(', ')}</td>\n              <td className=\"text-end\">{asset.annual_interest ? `${asset.annual_interest}%` : 'N/A'}</td>\n              <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.value)}</td>\n              <td className=\"text-center\">{new Date(asset.update_date).toLocaleDateString('fr-FR')}</td>\n              <td className=\"text-center\">\n                <Button variant=\"outline-primary\" size=\"sm\" onClick={() => { setSelectedAsset(asset); setShowModal(true); }}>Modifier</Button>{' '}\n                <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(asset.id)}>Supprimer</Button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>{selectedAsset?.id ? 'Modifier' : 'Ajouter'} un Actif</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <AssetForm asset={selectedAsset} onSave={handleSave} onCancel={() => setShowModal(false)} />\n        </Modal.Body>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Assets;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAC7D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuB/E,MAAMC,SAA+F,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrI,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IACrCgB,IAAI,EAAE,CAAAN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,IAAI,KAAI,EAAE;IACvBC,KAAK,EAAE,CAAAP,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,KAAK,KAAI,CAAC;IACxBC,eAAe,EAAE,CAAAR,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,eAAe,KAAI,IAAI;IAC/CC,KAAK,EAAE,CAAAT,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAES,KAAK,KAAI,EAAE;IACzBC,UAAU,EAAE,CAAAV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEU,UAAU,KAAI,CAAC;MAAEJ,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IAC/DI,WAAW,EAAEX,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEW,WAAW,GAAG,IAAIC,IAAI,CAACZ,KAAK,CAACW,WAAW,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACrI,CAAC,CAAC;EAEF,MAAMC,oBAAoB,GAAGA,CAACC,KAAa,EAAEC,KAAa,EAAEV,KAAU,KAAK;IACvE,MAAMW,aAAa,GAAG,CAAC,GAAGd,QAAQ,CAACM,UAAU,CAAC;IAC9CQ,aAAa,CAACF,KAAK,CAAC,GAAG;MAAE,GAAGE,aAAa,CAACF,KAAK,CAAC;MAAE,CAACC,KAAK,GAAGV;IAAM,CAAC;IAClEF,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEM,UAAU,EAAEQ;IAAc,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtBd,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEM,UAAU,EAAE,CAAC,GAAGN,QAAQ,CAACM,UAAU,EAAE;QAAEJ,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAE,CAAC;IAAE,CAAC,CAAC;EACpG,CAAC;EAED,MAAMa,cAAc,GAAIJ,KAAa,IAAK;IACtC,MAAME,aAAa,GAAG,CAAC,GAAGd,QAAQ,CAACM,UAAU,CAAC;IAC9CQ,aAAa,CAACG,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;IAC9BX,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEM,UAAU,EAAEQ;IAAc,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOC,CAAY,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAGzB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE0B,EAAE,GAAG,KAAK,GAAG,MAAM;IACzC,MAAMC,GAAG,GAAG3B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE0B,EAAE,GAAG,WAAW1B,KAAK,CAAC0B,EAAE,EAAE,GAAG,SAAS;IAEzD,IAAI;MACA,MAAMnC,SAAS,CAACkC,MAAM,CAAC,CAACE,GAAG,EAAEvB,QAAQ,CAAC;MACtCH,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACnE;EACJ,CAAC;EAED,oBACI9B,OAAA,CAACJ,IAAI;IAACoC,QAAQ,EAAER,YAAa;IAAAS,QAAA,gBACzBjC,OAAA,CAACJ,IAAI,CAACsC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxBjC,OAAA,CAACJ,IAAI,CAACwC,KAAK;QAAAH,QAAA,EAAC;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5BxC,OAAA,CAACJ,IAAI,CAAC6C,OAAO;QAACC,IAAI,EAAC,MAAM;QAACjC,KAAK,EAAEH,QAAQ,CAACE,IAAK;QAACmC,QAAQ,EAAElB,CAAC,IAAIlB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEE,IAAI,EAAEiB,CAAC,CAACmB,MAAM,CAACnC;QAAM,CAAC,CAAE;QAACoC,QAAQ;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtH,CAAC,eACbxC,OAAA,CAACJ,IAAI,CAACsC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxBjC,OAAA,CAACJ,IAAI,CAACwC,KAAK;QAAAH,QAAA,EAAC;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC/BxC,OAAA,CAACJ,IAAI,CAAC6C,OAAO;QAACC,IAAI,EAAC,QAAQ;QAACjC,KAAK,EAAEH,QAAQ,CAACG,KAAM;QAACkC,QAAQ,EAAElB,CAAC,IAAIlB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEG,KAAK,EAAEqC,UAAU,CAACrB,CAAC,CAACmB,MAAM,CAACnC,KAAK;QAAE,CAAC,CAAE;QAACoC,QAAQ;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtI,CAAC,eACbxC,OAAA,CAACJ,IAAI,CAACsC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxBjC,OAAA,CAACJ,IAAI,CAACwC,KAAK;QAAAH,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACvCxC,OAAA,CAACJ,IAAI,CAAC6C,OAAO;QAACC,IAAI,EAAC,QAAQ;QAACjC,KAAK,EAAEH,QAAQ,CAACI,eAAe,IAAI,EAAG;QAACiC,QAAQ,EAAElB,CAAC,IAAIlB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEI,eAAe,EAAEoC,UAAU,CAACrB,CAAC,CAACmB,MAAM,CAACnC,KAAK,CAAC,IAAI;QAAK,CAAC;MAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/J,CAAC,eACbxC,OAAA,CAACJ,IAAI,CAACsC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxBjC,OAAA,CAACJ,IAAI,CAACwC,KAAK;QAAAH,QAAA,EAAC;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC9BxC,OAAA,CAACJ,IAAI,CAAC6C,OAAO;QAACC,IAAI,EAAC,MAAM;QAACjC,KAAK,EAAEH,QAAQ,CAACK,KAAK,IAAI,EAAG;QAACgC,QAAQ,EAAElB,CAAC,IAAIlB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEK,KAAK,EAAEc,CAAC,CAACmB,MAAM,CAACnC;QAAM,CAAC;MAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrH,CAAC,eAEbxC,OAAA;MAAAiC,QAAA,EAAI;IAAU;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAClBlC,QAAQ,CAACM,UAAU,CAACmC,GAAG,CAAC,CAACC,QAAQ,EAAE9B,KAAK,kBACrClB,OAAA,CAACH,UAAU;MAACsC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxBjC,OAAA,CAACJ,IAAI,CAACqD,MAAM;QAACxC,KAAK,EAAEuC,QAAQ,CAACxC,IAAK;QAACmC,QAAQ,EAAElB,CAAC,IAAIR,oBAAoB,CAACC,KAAK,EAAE,MAAM,EAAEO,CAAC,CAACmB,MAAM,CAACnC,KAAK,CAAE;QAAAwB,QAAA,gBAClGjC,OAAA;UAAAiC,QAAA,EAAQ;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvBxC,OAAA;UAAAiC,QAAA,EAAQ;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3BxC,OAAA;UAAAiC,QAAA,EAAQ;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9BxC,OAAA;UAAAiC,QAAA,EAAQ;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpCxC,OAAA;UAAAiC,QAAA,EAAQ;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChCxC,OAAA;UAAAiC,QAAA,EAAQ;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACdxC,OAAA,CAACF,WAAW;QAAC4C,IAAI,EAAC,QAAQ;QAACjC,KAAK,EAAEuC,QAAQ,CAACvC,KAAM;QAACkC,QAAQ,EAAElB,CAAC,IAAIR,oBAAoB,CAACC,KAAK,EAAE,OAAO,EAAE4B,UAAU,CAACrB,CAAC,CAACmB,MAAM,CAACnC,KAAK,CAAC;MAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrIxC,OAAA,CAACL,MAAM;QAACuD,OAAO,EAAC,gBAAgB;QAACC,OAAO,EAAEA,CAAA,KAAM7B,cAAc,CAACJ,KAAK,CAAE;QAAAe,QAAA,EAAC;MAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,GAVnDtB,KAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAW3B,CACf,CAAC,eACFxC,OAAA,CAACL,MAAM;MAACuD,OAAO,EAAC,iBAAiB;MAACC,OAAO,EAAE9B,WAAY;MAAAY,QAAA,EAAC;IAAqB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEtFxC,OAAA,CAACJ,IAAI,CAACsC,KAAK;MAACC,SAAS,EAAC,WAAW;MAAAF,QAAA,gBAC7BjC,OAAA,CAACJ,IAAI,CAACwC,KAAK;QAAAH,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5CxC,OAAA,CAACJ,IAAI,CAAC6C,OAAO;QAACC,IAAI,EAAC,MAAM;QAACjC,KAAK,EAAEH,QAAQ,CAACO,WAAY;QAAC8B,QAAQ,EAAElB,CAAC,IAAIlB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEO,WAAW,EAAEY,CAAC,CAACmB,MAAM,CAACnC;QAAM,CAAC,CAAE;QAACoC,QAAQ;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpI,CAAC,eACbxC,OAAA,CAACL,MAAM;MAACuD,OAAO,EAAC,SAAS;MAACR,IAAI,EAAC,QAAQ;MAAAT,QAAA,EAAC;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC5DxC,OAAA,CAACL,MAAM;MAACuD,OAAO,EAAC,WAAW;MAACC,OAAO,EAAE/C,QAAS;MAAC+B,SAAS,EAAC,MAAM;MAAAF,QAAA,EAAC;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9E,CAAC;AAEf,CAAC;AAACnC,EAAA,CAnFIJ,SAA+F;AAAAmD,EAAA,GAA/FnD,SAA+F;AAsFrG,MAAMoD,MAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAE6B,QAAQ,CAAC,GAAGnE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAwB,IAAI,CAAC;EAC/E,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAM0E,WAAW,GAAGA,CAAA,KAAM;IACxBR,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEdlE,SAAS,CAAC0E,GAAG,CAAC,SAAS,CAAC,CACrBC,IAAI,CAACC,QAAQ,IAAI;MAChBb,SAAS,CAACa,QAAQ,CAACC,IAAI,CAAC;MACxBZ,UAAU,CAAC,KAAK,CAAC;MACjBO,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDM,KAAK,CAACzC,KAAK,IAAI;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAEzC,IAAIkC,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAMP,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;MACvC,CAAC,MAAM;QACLP,QAAQ,CAAC,4CAA4C,CAAC;QACtDD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDnE,SAAS,CAAC,MAAM;IACd2E,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvBb,YAAY,CAAC,KAAK,CAAC;IACnBE,gBAAgB,CAAC,IAAI,CAAC;IACtBG,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMS,YAAY,GAAG,MAAO/C,EAAU,IAAK;IACzC,IAAIgD,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAClE,IAAI;QACA,MAAMpF,SAAS,CAACqF,MAAM,CAAC,WAAWlD,EAAE,EAAE,CAAC;QACvCsC,WAAW,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOpC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE;IACJ;EACF,CAAC;EAED,IAAI2B,OAAO,IAAI,CAACG,SAAS,EAAE,oBAAO5D,OAAA;IAAAiC,QAAA,EAAG;EAAa;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACtD,IAAIV,KAAK,EAAE,oBACT9B,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAGmC,SAAS,EAAC,aAAa;MAAAF,QAAA,EAAEH;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtCxC,OAAA;MAAQmC,SAAS,EAAC,iBAAiB;MAACgB,OAAO,EAAEe,WAAY;MAAAjC,QAAA,EAAC;IAE1D;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAGR,oBACExC,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAKmC,SAAS,EAAC,wDAAwD;MAAAF,QAAA,gBACrEjC,OAAA;QAAAiC,QAAA,EAAI;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCxC,OAAA,CAACL,MAAM;QAACuD,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAM;UAAEY,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAAEF,YAAY,CAAC,IAAI,CAAC;QAAE,CAAE;QAAA5B,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9G,CAAC,eACNxC,OAAA;MAAOmC,SAAS,EAAC,iCAAiC;MAAAF,QAAA,gBAChDjC,OAAA;QAAOmC,SAAS,EAAC,YAAY;QAAAF,QAAA,eAC3BjC,OAAA;UAAAiC,QAAA,gBACEjC,OAAA;YAAAiC,QAAA,EAAI;UAAG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZxC,OAAA;YAAAiC,QAAA,EAAI;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBxC,OAAA;YAAImC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CxC,OAAA;YAAImC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCxC,OAAA;YAAImC,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CxC,OAAA;YAAImC,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRxC,OAAA;QAAAiC,QAAA,EACGsB,MAAM,CAACR,GAAG,CAAC7C,KAAK,iBACfF,OAAA;UAAAiC,QAAA,gBACEjC,OAAA;YAAAiC,QAAA,EAAK/B,KAAK,CAACM;UAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBxC,OAAA;YAAAiC,QAAA,EAAK/B,KAAK,CAACU,UAAU,CAACmC,GAAG,CAACgC,CAAC,IAAI,GAAGA,CAAC,CAACvE,IAAI,KAAK,IAAIwE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAACL,CAAC,CAACtE,KAAK,CAAC,GAAG,CAAC,CAAC4E,IAAI,CAAC,IAAI;UAAC;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxJxC,OAAA;YAAImC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE/B,KAAK,CAACQ,eAAe,GAAG,GAAGR,KAAK,CAACQ,eAAe,GAAG,GAAG;UAAK;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3FxC,OAAA;YAAImC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAI+C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAAClF,KAAK,CAACO,KAAK;UAAC;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1HxC,OAAA;YAAImC,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAE,IAAInB,IAAI,CAACZ,KAAK,CAACW,WAAW,CAAC,CAACyE,kBAAkB,CAAC,OAAO;UAAC;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1FxC,OAAA;YAAImC,SAAS,EAAC,aAAa;YAAAF,QAAA,gBACzBjC,OAAA,CAACL,MAAM;cAACuD,OAAO,EAAC,iBAAiB;cAACqC,IAAI,EAAC,IAAI;cAACpC,OAAO,EAAEA,CAAA,KAAM;gBAAEY,gBAAgB,CAAC7D,KAAK,CAAC;gBAAE2D,YAAY,CAAC,IAAI,CAAC;cAAE,CAAE;cAAA5B,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAClIxC,OAAA,CAACL,MAAM;cAACuD,OAAO,EAAC,gBAAgB;cAACqC,IAAI,EAAC,IAAI;cAACpC,OAAO,EAAEA,CAAA,KAAMwB,YAAY,CAACzE,KAAK,CAAC0B,EAAE,CAAE;cAAAK,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC;QAAA,GATEtC,KAAK,CAAC0B,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUb,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAERxC,OAAA,CAACN,KAAK;MAAC8F,IAAI,EAAE5B,SAAU;MAAC6B,MAAM,EAAEA,CAAA,KAAM5B,YAAY,CAAC,KAAK,CAAE;MAAA5B,QAAA,gBACxDjC,OAAA,CAACN,KAAK,CAACgG,MAAM;QAACC,WAAW;QAAA1D,QAAA,eACvBjC,OAAA,CAACN,KAAK,CAACkG,KAAK;UAAA3D,QAAA,GAAE6B,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAElC,EAAE,GAAG,UAAU,GAAG,SAAS,EAAC,WAAS;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACfxC,OAAA,CAACN,KAAK,CAACmG,IAAI;QAAA5D,QAAA,eACTjC,OAAA,CAACC,SAAS;UAACC,KAAK,EAAE4D,aAAc;UAAC3D,MAAM,EAAEuE,UAAW;UAACtE,QAAQ,EAAEA,CAAA,KAAMyD,YAAY,CAAC,KAAK;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACc,GAAA,CA1GID,MAAgB;AAAAyC,GAAA,GAAhBzC,MAAgB;AA4GtB,eAAeA,MAAM;AAAC,IAAAD,EAAA,EAAA0C,GAAA;AAAAC,YAAA,CAAA3C,EAAA;AAAA2C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}