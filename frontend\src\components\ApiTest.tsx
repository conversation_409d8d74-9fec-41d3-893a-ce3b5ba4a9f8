import React, { useState } from 'react';
import apiClient from './ApiClient';

const ApiTest: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testDashboard = async () => {
    setLoading(true);
    setResult('Testing...');
    
    try {
      const response = await apiClient.get('/dashboard');
      setResult(`Success: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error: any) {
      setResult(`Error: ${error.message}\nDetails: ${JSON.stringify({
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          baseURL: error.config?.baseURL,
          method: error.config?.method
        }
      }, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch = async () => {
    setLoading(true);
    setResult('Testing with fetch...');

    try {
      const response = await fetch('http://localhost:8000/api/dashboard');
      const data = await response.json();
      setResult(`Fetch Success: ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      setResult(`Fetch Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch127 = async () => {
    setLoading(true);
    setResult('Testing with fetch (127.0.0.1)...');

    try {
      const response = await fetch('http://127.0.0.1:8000/api/dashboard');
      const data = await response.json();
      setResult(`Fetch Success (127.0.0.1): ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      setResult(`Fetch Error (127.0.0.1): ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mt-4">
      <h2>API Test</h2>
      <div className="mb-3">
        <button 
          className="btn btn-primary me-2" 
          onClick={testDashboard}
          disabled={loading}
        >
          Test Dashboard API (Axios)
        </button>
        <button
          className="btn btn-secondary me-2"
          onClick={testDirectFetch}
          disabled={loading}
        >
          Test Dashboard API (Fetch localhost)
        </button>
        <button
          className="btn btn-info"
          onClick={testDirectFetch127}
          disabled={loading}
        >
          Test Dashboard API (Fetch 127.0.0.1)
        </button>
      </div>
      <pre className="bg-light p-3" style={{ whiteSpace: 'pre-wrap' }}>
        {result}
      </pre>
    </div>
  );
};

export default ApiTest;
