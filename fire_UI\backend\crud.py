from sqlalchemy.orm import Session, joinedload
import models, schemas

# Asset CRUD
def get_asset(db: Session, asset_id: int):
    return db.query(models.Asset).options(joinedload(models.Asset.categories).joinedload(models.AssetCategory.category)).filter(models.Asset.id == asset_id).first()

def get_assets(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Asset).options(joinedload(models.Asset.categories).joinedload(models.AssetCategory.category)).offset(skip).limit(limit).all()

def create_asset(db: Session, asset: schemas.AssetCreate):
    db_asset = models.Asset(name=asset.name, value=asset.value, annual_interest=asset.annual_interest, notes=asset.notes, update_date=asset.update_date)
    db.add(db_asset)
    db.flush() # Flush to get db_asset.id

    for category_value in asset.categories:
        category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
        if not category:
            category = models.Category(name=category_value.name)
            db.add(category)
            db.flush() # Flush to get category.id

        asset_category = models.AssetCategory(asset_id=db_asset.id, category_id=category.id, value=category_value.value)
        db.add(asset_category)
    
    db.commit()
    db.refresh(db_asset)
    return db_asset

def update_asset(db: Session, asset_id: int, asset: schemas.AssetCreate):
    db_asset = get_asset(db, asset_id)
    if db_asset:
        db_asset.name = asset.name
        db_asset.value = asset.value
        db_asset.annual_interest = asset.annual_interest
        db_asset.update_date = asset.update_date
        
        # Clear existing categories and add new ones
        for ac in db_asset.categories:
            db.delete(ac)
        db.flush()

        for category_value in asset.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush()

            asset_category = models.AssetCategory(asset_id=db_asset.id, category_id=category.id, value=category_value.value)
            db.add(asset_category)
            
        db.commit()
        db.refresh(db_asset)
    return db_asset

def delete_asset(db: Session, asset_id: int):
    db_asset = get_asset(db, asset_id)
    if db_asset:
        db.delete(db_asset)
        db.commit()
    return db_asset

# Liability CRUD
def get_liability(db: Session, liability_id: int):
    return db.query(models.Liability).filter(models.Liability.id == liability_id).first()

def get_liabilities(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Liability).offset(skip).limit(limit).all()

def create_liability(db: Session, liability: schemas.LiabilityCreate):
    db_liability = models.Liability(**liability.model_dump())
    db.add(db_liability)
    db.commit()
    db.refresh(db_liability)
    return db_liability

def update_liability(db: Session, liability_id: int, liability: schemas.LiabilityCreate):
    db_liability = get_liability(db, liability_id)
    if db_liability:
        for key, value in liability.model_dump().items():
            setattr(db_liability, key, value)
        db.commit()
        db.refresh(db_liability)
    return db_liability

def delete_liability(db: Session, liability_id: int):
    db_liability = get_liability(db, liability_id)
    if db_liability:
        db.delete(db_liability)
        db.commit()
    return db_liability

# PatrimoineHistory CRUD
def get_patrimoine_history(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.PatrimoineHistory).offset(skip).limit(limit).all()

def create_patrimoine_history(db: Session, history: schemas.PatrimoineHistoryCreate):
    db_history = models.PatrimoineHistory(**history.model_dump())
    db.add(db_history)
    db.commit()
    db.refresh(db_history)
    return db_history

# SCPI CRUD
def get_scpi(db: Session, scpi_id: int):
    return db.query(models.SCPI).filter(models.SCPI.id == scpi_id).first()

def get_scpis(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.SCPI).offset(skip).limit(limit).all()

def create_scpi(db: Session, scpi: schemas.SCPICreate):
    db_scpi = models.SCPI(**scpi.model_dump())
    db.add(db_scpi)
    db.commit()
    db.refresh(db_scpi)
    return db_scpi

def update_scpi(db: Session, scpi_id: int, scpi: schemas.SCPICreate):
    db_scpi = db.query(models.SCPI).filter(models.SCPI.id == scpi_id).first()
    if db_scpi:
        for key, value in scpi.model_dump().items():
            setattr(db_scpi, key, value)
        db.commit()
        db.refresh(db_scpi)
    return db_scpi

def delete_scpi(db: Session, scpi_id: int):
    db_scpi = db.query(models.SCPI).filter(models.SCPI.id == scpi_id).first()
    if db_scpi:
        db.delete(db_scpi)
        db.commit()
    return db_scpi