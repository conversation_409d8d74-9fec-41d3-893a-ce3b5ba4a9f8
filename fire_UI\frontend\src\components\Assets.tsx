import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';

interface CategoryValue {
  name: string;
  value: number;
}

interface Asset {
  id: number;
  name: string;
  value: number;
  annual_interest: number | null;
  notes: string | null;
  update_date: string;
  categories: CategoryValue[];
}

const AssetForm: React.FC<{ asset: Partial<Asset> | null, onSave: () => void, onCancel: () => void }> = ({ asset, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
        name: asset?.name || '',
        value: asset?.value || 0,
        annual_interest: asset?.annual_interest || null,
        notes: asset?.notes || '',
        categories: asset?.categories || [{ name: 'Bourse', value: 0 }],
        update_date: asset?.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    });

    const handleCategoryChange = (index: number, field: string, value: any) => {
        const newCategories = [...formData.categories];
        newCategories[index] = { ...newCategories[index], [field]: value };
        setFormData({ ...formData, categories: newCategories });
    };

    const addCategory = () => {
        setFormData({ ...formData, categories: [...formData.categories, { name: 'Bourse', value: 0 }] });
    };

    const removeCategory = (index: number) => {
        const newCategories = [...formData.categories];
        newCategories.splice(index, 1);
        setFormData({ ...formData, categories: newCategories });
    };

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        const method = asset?.id ? 'put' : 'post';
        const url = asset?.id ? `/assets/${asset.id}` : '/assets';
        
        try {
            await apiClient[method](url, formData);
            onSave();
        } catch (error) {
            console.error("Erreur lors de la sauvegarde de l'actif", error);
        }
    };

    return (
        <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
                <Form.Label>Nom</Form.Label>
                <Form.Control type="text" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Valeur</Form.Label>
                <Form.Control type="number" value={formData.value} onChange={e => setFormData({ ...formData, value: parseFloat(e.target.value) })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Intérêt Annuel</Form.Label>
                <Form.Control type="number" value={formData.annual_interest || ''} onChange={e => setFormData({ ...formData, annual_interest: parseFloat(e.target.value) || null })} />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Notes</Form.Label>
                <Form.Control type="text" value={formData.notes || ''} onChange={e => setFormData({ ...formData, notes: e.target.value })} />
            </Form.Group>
            
            <h5>Catégories</h5>
            {formData.categories.map((category, index) => (
                <InputGroup className="mb-3" key={index}>
                    <Form.Select value={category.name} onChange={e => handleCategoryChange(index, 'name', e.target.value)}>
                        <option>Bourse</option>
                        <option>Immobilier</option>
                        <option>Crypto-actifs</option>
                        <option>Prêts Participatifs</option>
                        <option>Fonds Sécurisés</option>
                        <option>Liquidités</option>
                    </Form.Select>
                    <FormControl type="number" value={category.value} onChange={e => handleCategoryChange(index, 'value', parseFloat(e.target.value))} />
                    <Button variant="outline-danger" onClick={() => removeCategory(index)}>X</Button>
                </InputGroup>
            ))}
            <Button variant="outline-primary" onClick={addCategory}>Ajouter une catégorie</Button>

            <Form.Group className="mb-3 mt-3">
                <Form.Label>Date de mise à jour</Form.Label>
                <Form.Control type="date" value={formData.update_date} onChange={e => setFormData({ ...formData, update_date: e.target.value })} required />
            </Form.Group>
            <Button variant="primary" type="submit">Sauvegarder</Button>
            <Button variant="secondary" onClick={onCancel} className="ms-2">Annuler</Button>
        </Form>
    );
};


const Assets: React.FC = () => {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Partial<Asset> | null>(null);

  const fetchAssets = () => {
    setLoading(true);
    apiClient.get('/assets')
      .then(response => {
        setAssets(response.data);
        setLoading(false);
      })
      .catch(error => {
        setError('Erreur lors de la récupération des actifs.');
        setLoading(false);
        console.error(error);
      });
  };

  useEffect(() => {
    fetchAssets();
  }, []);

  const handleSave = () => {
    setShowModal(false);
    setSelectedAsset(null);
    fetchAssets();
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cet actif ?")) {
        try {
            await apiClient.delete(`/assets/${id}`);
            fetchAssets();
        } catch (error) {
            console.error("Erreur lors de la suppression de l'actif", error);
        }
    }
  };

  if (loading && !showModal) return <p>Chargement...</p>;
  if (error) return <p className="text-danger">{error}</p>;

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Mon Patrimoine (Actifs)</h1>
        <Button variant="primary" onClick={() => { setSelectedAsset({}); setShowModal(true); }}>Ajouter un Actif</Button>
      </div>
      <table className="table table-striped table-hover">
        <thead className="table-dark">
          <tr>
            <th>Nom</th>
            <th>Catégories</th>
            <th className="text-end">Intérêt Annuel</th>
            <th className="text-end">Valeur</th>
            <th className="text-center">Dernière MàJ</th>
            <th className="text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          {assets.map(asset => (
            <tr key={asset.id}>
              <td>{asset.name}</td>
              <td>{asset.categories.map(c => `${c.name} (${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(c.value)})`).join(', ')}</td>
              <td className="text-end">{asset.annual_interest ? `${asset.annual_interest}%` : 'N/A'}</td>
              <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.value)}</td>
              <td className="text-center">{new Date(asset.update_date).toLocaleDateString('fr-FR')}</td>
              <td className="text-center">
                <Button variant="outline-primary" size="sm" onClick={() => { setSelectedAsset(asset); setShowModal(true); }}>Modifier</Button>{' '}
                <Button variant="outline-danger" size="sm" onClick={() => handleDelete(asset.id)}>Supprimer</Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{selectedAsset?.id ? 'Modifier' : 'Ajouter'} un Actif</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <AssetForm asset={selectedAsset} onSave={handleSave} onCancel={() => setShowModal(false)} />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default Assets;
