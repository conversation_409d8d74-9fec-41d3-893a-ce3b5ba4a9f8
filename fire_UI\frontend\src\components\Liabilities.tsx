import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { Modal, Button, Form } from 'react-bootstrap';

interface Liability {
  id: number;
  name: string;
  initial_amount: number;
  remaining_capital: number;
  interest_rate: number;
  end_date: string;
  monthly_payment: number;
}

const LiabilityForm: React.FC<{ liability: Partial<Liability> | null, onSave: () => void, onCancel: () => void }> = ({ liability, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
        name: liability?.name || '',
        initial_amount: liability?.initial_amount || 0,
        remaining_capital: liability?.remaining_capital || 0,
        interest_rate: liability?.interest_rate || 0,
        monthly_payment: liability?.monthly_payment || 0,
        end_date: liability?.end_date ? new Date(liability.end_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    });

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        const method = liability?.id ? 'put' : 'post';
        const url = liability?.id ? `/liabilities/${liability.id}` : '/liabilities';
        
        try {
            await apiClient[method](url, formData);
            onSave();
        } catch (error) {
            console.error("Erreur lors de la sauvegarde de l'emprunt", error);
        }
    };

    return (
        <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
                <Form.Label>Nom</Form.Label>
                <Form.Control type="text" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Capital Restant Dû</Form.Label>
                <Form.Control type="number" value={formData.remaining_capital} onChange={e => setFormData({ ...formData, remaining_capital: parseFloat(e.target.value) })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Mensualité</Form.Label>
                <Form.Control type="number" value={formData.monthly_payment} onChange={e => setFormData({ ...formData, monthly_payment: parseFloat(e.target.value) })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Taux d'intérêt (%)</Form.Label>
                <Form.Control type="number" step="0.01" value={formData.interest_rate} onChange={e => setFormData({ ...formData, interest_rate: parseFloat(e.target.value) })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Date de fin</Form.Label>
                <Form.Control type="date" value={formData.end_date} onChange={e => setFormData({ ...formData, end_date: e.target.value })} required />
            </Form.Group>
            <Button variant="primary" type="submit">Sauvegarder</Button>
            <Button variant="secondary" onClick={onCancel} className="ms-2">Annuler</Button>
        </Form>
    );
};


const Liabilities: React.FC = () => {
  const [liabilities, setLiabilities] = useState<Liability[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedLiability, setSelectedLiability] = useState<Partial<Liability> | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const fetchLiabilities = () => {
    setLoading(true);
    setError(null);

    apiClient.get('/liabilities')
      .then(response => {
        setLiabilities(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('Liabilities API error:', error);

        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchLiabilities(), 1000);
        } else {
          setError('Erreur lors de la récupération des emprunts.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchLiabilities();
  }, []);

  const handleSave = () => {
    setShowModal(false);
    setSelectedLiability(null);
    fetchLiabilities();
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cet emprunt ?")) {
        try {
            await apiClient.delete(`/liabilities/${id}`);
            fetchLiabilities();
        } catch (error) {
            console.error("Erreur lors de la suppression de l'emprunt", error);
        }
    }
  };

  if (loading && !showModal) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchLiabilities}>
        Réessayer
      </button>
    </div>
  );

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Mes Emprunts (Passifs)</h1>
        <Button variant="primary" onClick={() => { setSelectedLiability({}); setShowModal(true); }}>Ajouter un Emprunt</Button>
      </div>
      <table className="table table-striped table-hover">
        <thead className="table-dark">
          <tr>
            <th>Nom</th>
            <th className="text-end">Capital Restant Dû</th>
            <th className="text-end">Mensualité</th>
            <th className="text-center">Taux</th>
            <th className="text-center">Date de Fin</th>
            <th className="text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          {liabilities.map(liability => (
            <tr key={liability.id}>
              <td>{liability.name}</td>
              <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(liability.remaining_capital)}</td>
              <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(liability.monthly_payment)}</td>
              <td className="text-center">{liability.interest_rate}%</td>
              <td className="text-center">{new Date(liability.end_date).toLocaleDateString('fr-FR')}</td>
              <td className="text-center">
                <Button variant="outline-primary" size="sm" onClick={() => { setSelectedLiability(liability); setShowModal(true); }}>Modifier</Button>{' '}
                <Button variant="outline-danger" size="sm" onClick={() => handleDelete(liability.id)}>Supprimer</Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <Modal show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{selectedLiability?.id ? 'Modifier' : 'Ajouter'} un Emprunt</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <LiabilityForm liability={selectedLiability} onSave={handleSave} onCancel={() => setShowModal(false)} />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default Liabilities;
