{"ast": null, "code": "import axios from 'axios';\nconst apiClient = axios.create({\n  baseURL: 'http://localhost:8000/api',\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 10000 // 10 second timeout\n});\n\n// Add response interceptor for error handling\napiClient.interceptors.response.use(response => response, error => {\n  var _error$response, _error$config;\n  console.error('API Error:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status, (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url, error.message);\n  return Promise.reject(error);\n});\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "apiClient", "create", "baseURL", "headers", "timeout", "interceptors", "response", "use", "error", "_error$response", "_error$config", "console", "status", "config", "url", "message", "Promise", "reject"], "sources": ["D:/GIT/fire_UI/frontend/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst apiClient = axios.create({\n  baseURL: 'http://localhost:8000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  timeout: 10000, // 10 second timeout\n});\n\n// Add response interceptor for error handling\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('API Error:', error.response?.status, error.config?.url, error.message);\n    return Promise.reject(error);\n  }\n);\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC7BC,OAAO,EAAE,2BAA2B;EACpCC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK,CAAE;AAClB,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,QAAQ,CAACC,GAAG,CAChCD,QAAQ,IAAKA,QAAQ,EACrBE,KAAK,IAAK;EAAA,IAAAC,eAAA,EAAAC,aAAA;EACTC,OAAO,CAACH,KAAK,CAAC,YAAY,GAAAC,eAAA,GAAED,KAAK,CAACF,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,GAAAF,aAAA,GAAEF,KAAK,CAACK,MAAM,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,GAAG,EAAEN,KAAK,CAACO,OAAO,CAAC;EACrF,OAAOC,OAAO,CAACC,MAAM,CAACT,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}