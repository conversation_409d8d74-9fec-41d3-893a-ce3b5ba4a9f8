import React, { useEffect, useState } from 'react';
import apiClient from './ApiClient';
import { Doughn<PERSON> } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

interface DashboardData {
  net_patrimoine: number;
  total_assets: number;
  total_liabilities: number;
  allocation: { [key: string]: number };
}

const Dashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const fetchDashboardData = () => {
    console.log('Dashboard: Starting API call to /dashboard');
    setLoading(true);
    setError(null);

    apiClient.get('/dashboard')
      .then(response => {
        console.log('Dashboard: API call successful', response.data);
        setData(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('Dashboard: API call failed', error);
        console.error('Dashboard: Error details', {
          message: error.message,
          response: error.response,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });

        if (retryCount < 2) {
          console.log(`Dashboard: Retrying... (attempt ${retryCount + 1})`);
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchDashboardData(), 1000);
        } else {
          setError('Erreur lors de la récupération des données du dashboard.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  if (loading) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchDashboardData}>
        Réessayer
      </button>
    </div>
  );
  if (!data) return <p>Aucune donnée disponible.</p>;

  const chartData = {
    labels: Object.keys(data.allocation),
    datasets: [
      {
        label: 'Répartition du portefeuille',
        data: Object.values(data.allocation),
        backgroundColor: [
          'rgba(255, 99, 132, 0.7)',
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)',
          'rgba(255, 159, 64, 0.7)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div>
      <h1 className="mb-4">Dashboard</h1>
      <div className="row">
        <div className="col-md-4">
          <div className="card text-white bg-primary mb-3">
            <div className="card-header">Patrimoine Net Total</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.net_patrimoine)}</h5>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-success mb-3">
            <div className="card-header">Total Actifs</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_assets)}</h5>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-danger mb-3">
            <div className="card-header">Total Passifs</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_liabilities)}</h5>
            </div>
          </div>
        </div>
      </div>
      <div className="row mt-4">
        <div className="col-md-6">
          <h2>Répartition des Actifs</h2>
          <Doughnut data={chartData} />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;