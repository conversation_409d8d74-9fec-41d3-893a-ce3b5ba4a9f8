from fastapi import FastAP<PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List

import crud, models, schemas
from database import SessionLocal, engine, DATABASE_URL

print(f"Database URL: {DATABASE_URL}")



app = FastAPI()

origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost",
    "http://127.0.0.1",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/api/assets/", response_model=List[schemas.Asset])
def read_assets(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    assets = crud.get_assets(db, skip=skip, limit=limit)
    return assets

@app.post("/api/assets/", response_model=schemas.Asset)
def create_asset(asset: schemas.AssetCreate, db: Session = Depends(get_db)):
    return crud.create_asset(db=db, asset=asset)

@app.put("/api/assets/{asset_id}", response_model=schemas.Asset)
def update_asset(asset_id: int, asset: schemas.AssetCreate, db: Session = Depends(get_db)):
    db_asset = crud.update_asset(db, asset_id=asset_id, asset=asset)
    if db_asset is None:
        raise HTTPException(status_code=404, detail="Asset not found")
    return db_asset

@app.delete("/api/assets/{asset_id}", response_model=schemas.Asset)
def delete_asset(asset_id: int, db: Session = Depends(get_db)):
    db_asset = crud.delete_asset(db, asset_id=asset_id)
    if db_asset is None:
        raise HTTPException(status_code=404, detail="Asset not found")
    return db_asset

@app.get("/api/liabilities/", response_model=List[schemas.Liability])
def read_liabilities(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    liabilities = crud.get_liabilities(db, skip=skip, limit=limit)
    return liabilities

@app.post("/api/liabilities/", response_model=schemas.Liability)
def create_liability(liability: schemas.LiabilityCreate, db: Session = Depends(get_db)):
    return crud.create_liability(db=db, liability=liability)

@app.put("/api/liabilities/{liability_id}", response_model=schemas.Liability)
def update_liability(liability_id: int, liability: schemas.LiabilityCreate, db: Session = Depends(get_db)):
    db_liability = crud.update_liability(db, liability_id=liability_id, liability=liability)
    if db_liability is None:
        raise HTTPException(status_code=404, detail="Liability not found")
    return db_liability

@app.delete("/api/liabilities/{liability_id}", response_model=schemas.Liability)
def delete_liability(liability_id: int, db: Session = Depends(get_db)):
    db_liability = crud.delete_liability(db, liability_id=liability_id)
    if db_liability is None:
        raise HTTPException(status_code=404, detail="Liability not found")
    return db_liability

@app.get("/api/patrimoine-history/", response_model=List[schemas.PatrimoineHistory])
def read_patrimoine_history(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    history = crud.get_patrimoine_history(db, skip=skip, limit=limit)
    return history

@app.get("/api/dashboard")
def get_dashboard_data(db: Session = Depends(get_db)):
    total_assets = db.query(func.sum(models.Asset.value)).scalar() or 0
    total_liabilities = db.query(func.sum(models.Liability.remaining_capital)).scalar() or 0
    net_patrimoine = total_assets - total_liabilities

    allocation_data = db.query(
        models.Category.name,
        func.sum(models.AssetCategory.value).label('total_value')
    ).select_from(models.Category).join(models.AssetCategory).group_by(models.Category.name).all()

    allocation = {category: value for category, value in allocation_data}

    return {
        "net_patrimoine": net_patrimoine,
        "total_assets": total_assets,
        "total_liabilities": total_liabilities,
        "allocation": allocation
    }

@app.get("/api/fire-target")
def get_fire_target(db: Session = Depends(get_db)):
    # Hardcoded values from the plan
    fire_target_amount = 910150
    swr = 0.04

    total_assets = db.query(func.sum(models.Asset.value)).scalar() or 0
    total_liabilities = db.query(func.sum(models.Liability.remaining_capital)).scalar() or 0
    net_patrimoine = total_assets - total_liabilities

    remaining_to_invest = fire_target_amount - net_patrimoine
    potential_passive_income = net_patrimoine * swr

    return {
        "fire_target_amount": fire_target_amount,
        "secure_withdrawal_rate": swr,
        "current_net_patrimoine": net_patrimoine,
        "remaining_to_invest": remaining_to_invest,
        "potential_passive_income": potential_passive_income
    }
