from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List

import crud, models, schemas
from database import SessionLocal, engine, DATABASE_URL

print(f"Database URL: {DATABASE_URL}")



app = FastAPI()

origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost",
    "http://127.0.0.1",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/api/assets/")
def read_assets(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    try:
        assets = db.query(models.Asset).offset(skip).limit(limit).all()
        result = []
        for asset in assets:
            asset_data = {
                "id": asset.id,
                "name": asset.name,
                "value": asset.value,
                "annual_interest": asset.annual_interest,
                "notes": asset.notes,
                "update_date": asset.update_date.isoformat(),
                "categories": [],
                "liabilities": []
            }
            # Add categories
            for asset_category in asset.categories:
                asset_data["categories"].append({
                    "category_name": asset_category.category.name,
                    "value": asset_category.value
                })
            result.append(asset_data)
        return result
    except Exception as e:
        print(f"Error in read_assets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/assets/")
def create_asset(asset: schemas.AssetCreate, db: Session = Depends(get_db)):
    try:
        # Create the asset
        db_asset = models.Asset(
            name=asset.name,
            value=asset.value,
            annual_interest=asset.annual_interest,
            notes=asset.notes,
            update_date=asset.update_date
        )
        db.add(db_asset)
        db.flush()  # Get the asset ID

        # Add categories
        for category_value in asset.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush()

            asset_category = models.AssetCategory(
                asset_id=db_asset.id,
                category_id=category.id,
                value=category_value.value
            )
            db.add(asset_category)

        db.commit()
        db.refresh(db_asset)

        # Return the same format as GET
        result = {
            "id": db_asset.id,
            "name": db_asset.name,
            "value": db_asset.value,
            "annual_interest": db_asset.annual_interest,
            "notes": db_asset.notes,
            "update_date": db_asset.update_date.isoformat(),
            "categories": [],
            "liabilities": []
        }
        for asset_category in db_asset.categories:
            result["categories"].append({
                "category_name": asset_category.category.name,
                "value": asset_category.value
            })
        return result
    except Exception as e:
        db.rollback()
        print(f"Error in create_asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/assets/{asset_id}")
def update_asset(asset_id: int, asset: schemas.AssetCreate, db: Session = Depends(get_db)):
    try:
        # Find the existing asset
        db_asset = db.query(models.Asset).filter(models.Asset.id == asset_id).first()
        if db_asset is None:
            raise HTTPException(status_code=404, detail="Asset not found")

        # Update asset fields
        db_asset.name = asset.name
        db_asset.value = asset.value
        db_asset.annual_interest = asset.annual_interest
        db_asset.notes = asset.notes
        db_asset.update_date = asset.update_date

        # Delete existing categories
        db.query(models.AssetCategory).filter(models.AssetCategory.asset_id == asset_id).delete()

        # Add new categories
        for category_value in asset.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush()

            asset_category = models.AssetCategory(
                asset_id=db_asset.id,
                category_id=category.id,
                value=category_value.value
            )
            db.add(asset_category)

        db.commit()
        db.refresh(db_asset)

        # Return the same format as GET
        result = {
            "id": db_asset.id,
            "name": db_asset.name,
            "value": db_asset.value,
            "annual_interest": db_asset.annual_interest,
            "notes": db_asset.notes,
            "update_date": db_asset.update_date.isoformat(),
            "categories": [],
            "liabilities": []
        }
        for asset_category in db_asset.categories:
            result["categories"].append({
                "category_name": asset_category.category.name,
                "value": asset_category.value
            })
        return result
    except Exception as e:
        db.rollback()
        print(f"Error in update_asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/assets/{asset_id}")
def delete_asset(asset_id: int, db: Session = Depends(get_db)):
    try:
        # Find the existing asset
        db_asset = db.query(models.Asset).filter(models.Asset.id == asset_id).first()
        if db_asset is None:
            raise HTTPException(status_code=404, detail="Asset not found")

        # Store asset data before deletion
        result = {
            "id": db_asset.id,
            "name": db_asset.name,
            "value": db_asset.value,
            "annual_interest": db_asset.annual_interest,
            "notes": db_asset.notes,
            "update_date": db_asset.update_date.isoformat(),
            "categories": [],
            "liabilities": []
        }
        for asset_category in db_asset.categories:
            result["categories"].append({
                "category_name": asset_category.category.name,
                "value": asset_category.value
            })

        # Delete categories first
        db.query(models.AssetCategory).filter(models.AssetCategory.asset_id == asset_id).delete()

        # Delete the asset
        db.delete(db_asset)
        db.commit()

        return result
    except Exception as e:
        db.rollback()
        print(f"Error in delete_asset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/liabilities/", response_model=List[schemas.Liability])
def read_liabilities(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    liabilities = crud.get_liabilities(db, skip=skip, limit=limit)
    return liabilities

@app.post("/api/liabilities/", response_model=schemas.Liability)
def create_liability(liability: schemas.LiabilityCreate, db: Session = Depends(get_db)):
    return crud.create_liability(db=db, liability=liability)

@app.put("/api/liabilities/{liability_id}", response_model=schemas.Liability)
def update_liability(liability_id: int, liability: schemas.LiabilityCreate, db: Session = Depends(get_db)):
    db_liability = crud.update_liability(db, liability_id=liability_id, liability=liability)
    if db_liability is None:
        raise HTTPException(status_code=404, detail="Liability not found")
    return db_liability

@app.delete("/api/liabilities/{liability_id}", response_model=schemas.Liability)
def delete_liability(liability_id: int, db: Session = Depends(get_db)):
    db_liability = crud.delete_liability(db, liability_id=liability_id)
    if db_liability is None:
        raise HTTPException(status_code=404, detail="Liability not found")
    return db_liability

@app.get("/api/patrimoine-history/", response_model=List[schemas.PatrimoineHistory])
def read_patrimoine_history(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    history = crud.get_patrimoine_history(db, skip=skip, limit=limit)
    return history

@app.get("/api/scpi/", response_model=List[schemas.SCPI])
def read_scpis(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    scpis = crud.get_scpis(db, skip=skip, limit=limit)
    return scpis

@app.post("/api/scpi/", response_model=schemas.SCPI)
def create_scpi(scpi: schemas.SCPICreate, db: Session = Depends(get_db)):
    return crud.create_scpi(db=db, scpi=scpi)

@app.put("/api/scpi/{scpi_id}", response_model=schemas.SCPI)
def update_scpi(scpi_id: int, scpi: schemas.SCPICreate, db: Session = Depends(get_db)):
    db_scpi = crud.update_scpi(db, scpi_id=scpi_id, scpi=scpi)
    if db_scpi is None:
        raise HTTPException(status_code=404, detail="SCPI not found")
    return db_scpi

@app.delete("/api/scpi/{scpi_id}", response_model=schemas.SCPI)
def delete_scpi(scpi_id: int, db: Session = Depends(get_db)):
    db_scpi = crud.delete_scpi(db, scpi_id=scpi_id)
    if db_scpi is None:
        raise HTTPException(status_code=404, detail="SCPI not found")
    return db_scpi

@app.get("/api/dashboard")
def get_dashboard_data(db: Session = Depends(get_db)):
    # Calculate total assets (including SCPI)
    assets_total = db.query(func.sum(models.Asset.value)).scalar() or 0
    scpi_total = db.query(func.sum(models.SCPI.total_value)).scalar() or 0
    total_assets = assets_total + scpi_total

    total_liabilities = db.query(func.sum(models.Liability.remaining_capital)).scalar() or 0
    net_patrimoine = total_assets - total_liabilities

    # Get allocation from assets
    allocation_data = db.query(
        models.Category.name,
        func.sum(models.AssetCategory.value).label('total_value')
    ).select_from(models.Category).join(models.AssetCategory).group_by(models.Category.name).all()

    allocation = {category: value for category, value in allocation_data}

    # Add SCPI to Immobilier category
    if scpi_total > 0:
        if "Immobilier" in allocation:
            allocation["Immobilier"] += scpi_total
        else:
            allocation["Immobilier"] = scpi_total

    return {
        "net_patrimoine": net_patrimoine,
        "total_assets": total_assets,
        "total_liabilities": total_liabilities,
        "allocation": allocation
    }

@app.get("/api/fire-target")
def get_fire_target(db: Session = Depends(get_db)):
    # Hardcoded values from the plan
    fire_target_amount = 910150
    swr = 0.04

    total_assets = db.query(func.sum(models.Asset.value)).scalar() or 0
    total_liabilities = db.query(func.sum(models.Liability.remaining_capital)).scalar() or 0
    net_patrimoine = total_assets - total_liabilities

    remaining_to_invest = fire_target_amount - net_patrimoine
    potential_passive_income = net_patrimoine * swr

    return {
        "fire_target_amount": fire_target_amount,
        "secure_withdrawal_rate": swr,
        "current_net_patrimoine": net_patrimoine,
        "remaining_to_invest": remaining_to_invest,
        "potential_passive_income": potential_passive_income
    }
