{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(ArcElement, Tooltip, Legend);\nconst Dashboard = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchDashboardData = () => {\n    console.log('Dashboard: Starting API call to /dashboard');\n    setLoading(true);\n    setError(null);\n    apiClient.get('/dashboard').then(response => {\n      console.log('Dashboard: API call successful', response.data);\n      setData(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      var _error$response, _error$response2, _error$response3;\n      console.error('Dashboard: API call failed', error);\n      console.error('Dashboard: Error details', {\n        message: error.message,\n        response: error.response,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data\n      });\n      if (retryCount < 2) {\n        console.log(`Dashboard: Retrying... (attempt ${retryCount + 1})`);\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchDashboardData(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des données du dashboard.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchDashboardData,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 21\n  }, this);\n  const chartData = {\n    labels: Object.keys(data.allocation),\n    datasets: [{\n      label: 'Répartition du portefeuille',\n      data: Object.values(data.allocation),\n      backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)'],\n      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],\n      borderWidth: 1\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"mb-4\",\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-primary mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Patrimoine Net Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.net_patrimoine)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-success mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Actifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.total_assets)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-danger mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Passifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.total_liabilities)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"R\\xE9partition des Actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Doughnut, {\n          data: chartData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"OfDpPZA5krn7xMvjWiPniKd42GY=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Doughnut", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "retryCount", "setRetryCount", "fetchDashboardData", "console", "log", "get", "then", "response", "catch", "_error$response", "_error$response2", "_error$response3", "message", "status", "statusText", "prev", "setTimeout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "chartData", "labels", "Object", "keys", "allocation", "datasets", "label", "values", "backgroundColor", "borderColor", "borderWidth", "Intl", "NumberFormat", "style", "currency", "format", "net_patrimoine", "total_assets", "total_liabilities", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Doughn<PERSON> } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\n\nChartJS.register(ArcE<PERSON>, Tooltip, Legend);\n\ninterface DashboardData {\n  net_patrimoine: number;\n  total_assets: number;\n  total_liabilities: number;\n  allocation: { [key: string]: number };\n}\n\nconst Dashboard: React.FC = () => {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchDashboardData = () => {\n    console.log('Dashboard: Starting API call to /dashboard');\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/dashboard')\n      .then(response => {\n        console.log('Dashboard: API call successful', response.data);\n        setData(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('Dashboard: API call failed', error);\n        console.error('Dashboard: Error details', {\n          message: error.message,\n          response: error.response,\n          status: error.response?.status,\n          statusText: error.response?.statusText,\n          data: error.response?.data\n        });\n\n        if (retryCount < 2) {\n          console.log(`Dashboard: Retrying... (attempt ${retryCount + 1})`);\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchDashboardData(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des données du dashboard.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchDashboardData}>\n        Réessayer\n      </button>\n    </div>\n  );\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const chartData = {\n    labels: Object.keys(data.allocation),\n    datasets: [\n      {\n        label: 'Répartition du portefeuille',\n        data: Object.values(data.allocation),\n        backgroundColor: [\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n          'rgba(75, 192, 192, 0.7)',\n          'rgba(153, 102, 255, 0.7)',\n          'rgba(255, 159, 64, 0.7)',\n        ],\n        borderColor: [\n          'rgba(255, 99, 132, 1)',\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 206, 86, 1)',\n          'rgba(75, 192, 192, 1)',\n          'rgba(153, 102, 255, 1)',\n          'rgba(255, 159, 64, 1)',\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  return (\n    <div>\n      <h1 className=\"mb-4\">Dashboard</h1>\n      <div className=\"row\">\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-primary mb-3\">\n            <div className=\"card-header\">Patrimoine Net Total</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.net_patrimoine)}</h5>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-success mb-3\">\n            <div className=\"card-header\">Total Actifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_assets)}</h5>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-danger mb-3\">\n            <div className=\"card-header\">Total Passifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_liabilities)}</h5>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"row mt-4\">\n        <div className=\"col-md-6\">\n          <h2>Répartition des Actifs</h2>\n          <Doughnut data={chartData} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,KAAK,IAAIC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzEL,OAAO,CAACM,QAAQ,CAACL,UAAU,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAS7C,MAAMI,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMqB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzDP,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEdjB,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC,CACxBC,IAAI,CAACC,QAAQ,IAAI;MAChBJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEG,QAAQ,CAACb,IAAI,CAAC;MAC5DC,OAAO,CAACY,QAAQ,CAACb,IAAI,CAAC;MACtBG,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDO,KAAK,CAACV,KAAK,IAAI;MAAA,IAAAW,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdR,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAE;QACxCc,OAAO,EAAEd,KAAK,CAACc,OAAO;QACtBL,QAAQ,EAAET,KAAK,CAACS,QAAQ;QACxBM,MAAM,GAAAJ,eAAA,GAAEX,KAAK,CAACS,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBI,MAAM;QAC9BC,UAAU,GAAAJ,gBAAA,GAAEZ,KAAK,CAACS,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBI,UAAU;QACtCpB,IAAI,GAAAiB,gBAAA,GAAEb,KAAK,CAACS,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBjB;MACxB,CAAC,CAAC;MAEF,IAAIM,UAAU,GAAG,CAAC,EAAE;QAClBG,OAAO,CAACC,GAAG,CAAC,mCAAmCJ,UAAU,GAAG,CAAC,GAAG,CAAC;QACjEC,aAAa,CAACc,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAMd,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC;MAC9C,CAAC,MAAM;QACLH,QAAQ,CAAC,0DAA0D,CAAC;QACpEF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACdsB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,OAAO,EAAE,oBAAON,OAAA;IAAA2B,QAAA,EAAG;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIvB,KAAK,EAAE,oBACTR,OAAA;IAAA2B,QAAA,gBACE3B,OAAA;MAAGgC,SAAS,EAAC,aAAa;MAAAL,QAAA,EAAEnB;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtC/B,OAAA;MAAQgC,SAAS,EAAC,iBAAiB;MAACC,OAAO,EAAErB,kBAAmB;MAAAe,QAAA,EAAC;IAEjE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAER,IAAI,CAAC3B,IAAI,EAAE,oBAAOJ,OAAA;IAAA2B,QAAA,EAAG;EAAyB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAMG,SAAS,GAAG;IAChBC,MAAM,EAAEC,MAAM,CAACC,IAAI,CAACjC,IAAI,CAACkC,UAAU,CAAC;IACpCC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,6BAA6B;MACpCpC,IAAI,EAAEgC,MAAM,CAACK,MAAM,CAACrC,IAAI,CAACkC,UAAU,CAAC;MACpCI,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,CAC1B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,CACxB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,oBACE5C,OAAA;IAAA2B,QAAA,gBACE3B,OAAA;MAAIgC,SAAS,EAAC,MAAM;MAAAL,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnC/B,OAAA;MAAKgC,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClB3B,OAAA;QAAKgC,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvB3B,OAAA;UAAKgC,SAAS,EAAC,iCAAiC;UAAAL,QAAA,gBAC9C3B,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvD/B,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxB3B,OAAA;cAAIgC,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIkB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAAC7C,IAAI,CAAC8C,cAAc;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN/B,OAAA;QAAKgC,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvB3B,OAAA;UAAKgC,SAAS,EAAC,iCAAiC;UAAAL,QAAA,gBAC9C3B,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/C/B,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxB3B,OAAA;cAAIgC,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIkB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAAC7C,IAAI,CAAC+C,YAAY;YAAC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN/B,OAAA;QAAKgC,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvB3B,OAAA;UAAKgC,SAAS,EAAC,gCAAgC;UAAAL,QAAA,gBAC7C3B,OAAA;YAAKgC,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD/B,OAAA;YAAKgC,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxB3B,OAAA;cAAIgC,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIkB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAAC7C,IAAI,CAACgD,iBAAiB;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN/B,OAAA;MAAKgC,SAAS,EAAC,UAAU;MAAAL,QAAA,eACvB3B,OAAA;QAAKgC,SAAS,EAAC,UAAU;QAAAL,QAAA,gBACvB3B,OAAA;UAAA2B,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/B/B,OAAA,CAACP,QAAQ;UAACW,IAAI,EAAE8B;QAAU;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAtHID,SAAmB;AAAAmD,EAAA,GAAnBnD,SAAmB;AAwHzB,eAAeA,SAAS;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}