from pydantic import BaseModel, Field
from datetime import date
from typing import List, Optional

class LiabilityBase(BaseModel):
    name: str
    initial_amount: float
    remaining_capital: float
    interest_rate: float
    end_date: date
    monthly_payment: float
    asset_id: Optional[int] = None

class LiabilityCreate(LiabilityBase):
    pass

class Liability(LiabilityBase):
    id: int

    class Config:
        from_attributes = True

class CategoryValue(BaseModel):
    name: str
    value: float

class AssetCategorySchema(BaseModel):
    category_name: str
    value: float

    class Config:
        from_attributes = True

class AssetBase(BaseModel):
    name: str
    value: float
    annual_interest: Optional[float] = None
    notes: Optional[str] = None
    update_date: date

class AssetCreate(AssetBase):
    categories: List[CategoryValue]

class Asset(AssetBase):
    id: int
    liabilities: List[Liability] = []
    categories: List[AssetCategorySchema] = []

    class Config:
        from_attributes = True

class PatrimoineHistoryBase(BaseModel):
    date: date
    net_patrimoine: float

class PatrimoineHistoryCreate(PatrimoineHistoryBase):
    pass

class PatrimoineHistory(PatrimoineHistoryBase):
    id: int

    class Config:
        from_attributes = True

class SCPIBase(BaseModel):
    name: str
    price_per_share: float
    number_of_shares: int
    total_value: float
    update_date: date

class SCPICreate(SCPIBase):
    pass

class SCPI(SCPIBase):
    id: int

    class Config:
        from_attributes = True

class FireSettingsBase(BaseModel):
    fire_target_amount: float
    secure_withdrawal_rate: float
    update_date: date

class FireSettingsCreate(FireSettingsBase):
    pass

class FireSettingsUpdate(BaseModel):
    fire_target_amount: Optional[float] = None
    secure_withdrawal_rate: Optional[float] = None

class FireSettings(FireSettingsBase):
    id: int

    class Config:
        from_attributes = True

class PatrimoineEvolutionBase(BaseModel):
    annee: int
    investissement: float
    prix_part_scpi: float
    remboursement_credit: float
    valeur_reelle_scpi: float
    total_patrimoine: float
    evolution_pourcentage: Optional[float] = None
    evolution_euros: Optional[float] = None
    croissance_moyenne: Optional[float] = None
    tcam: Optional[float] = None

class PatrimoineEvolutionCreate(PatrimoineEvolutionBase):
    pass

class PatrimoineEvolutionUpdate(BaseModel):
    annee: Optional[int] = None
    investissement: Optional[float] = None
    prix_part_scpi: Optional[float] = None
    remboursement_credit: Optional[float] = None
    valeur_reelle_scpi: Optional[float] = None
    total_patrimoine: Optional[float] = None
    evolution_pourcentage: Optional[float] = None
    evolution_euros: Optional[float] = None
    croissance_moyenne: Optional[float] = None
    tcam: Optional[float] = None

class PatrimoineEvolution(PatrimoineEvolutionBase):
    id: int

    class Config:
        from_attributes = True
