{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\FireTarget.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FireTarget = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchFireTargetData = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/fire-target').then(response => {\n      setData(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('FireTarget API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchFireTargetData(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des données FIRE.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchFireTargetData();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"text-danger\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 21\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 21\n  }, this);\n  const formatCurrency = value => new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(value);\n  const progressPercentage = data.current_net_patrimoine / data.fire_target_amount * 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"mb-4\",\n      children: \"Objectif FIRE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: \"Progression vers votre Objectif\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Vous avez atteint \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formatCurrency(data.current_net_patrimoine)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 35\n              }, this), \" sur votre objectif de \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formatCurrency(data.fire_target_amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 120\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress\",\n              style: {\n                height: '25px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-bar progress-bar-striped progress-bar-animated\",\n                role: \"progressbar\",\n                style: {\n                  width: `${progressPercentage}%`\n                },\n                \"aria-valuenow\": progressPercentage,\n                \"aria-valuemin\": 0,\n                \"aria-valuemax\": 100,\n                children: [progressPercentage.toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Montant restant \\xE0 investir :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), \" \", formatCurrency(data.remaining_to_invest)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"Revenu passif annuel potentiel (SWR \", data.secure_withdrawal_rate * 100, \"%) :\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this), \" \", formatCurrency(data.potential_passive_income)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(FireTarget, \"OfDpPZA5krn7xMvjWiPniKd42GY=\");\n_c = FireTarget;\nexport default FireTarget;\nvar _c;\n$RefreshReg$(_c, \"FireTarget\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "jsxDEV", "_jsxDEV", "FireTarget", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "retryCount", "setRetryCount", "fetchFireTargetData", "get", "then", "response", "catch", "console", "prev", "setTimeout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "progressPercentage", "current_net_patrimoine", "fire_target_amount", "height", "role", "width", "toFixed", "remaining_to_invest", "secure_withdrawal_rate", "potential_passive_income", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/FireTarget.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\n\ninterface FireTargetData {\n  fire_target_amount: number;\n  secure_withdrawal_rate: number;\n  current_net_patrimoine: number;\n  remaining_to_invest: number;\n  potential_passive_income: number;\n}\n\nconst FireTarget: React.FC = () => {\n  const [data, setData] = useState<FireTargetData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchFireTargetData = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/fire-target')\n      .then(response => {\n        setData(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('FireTarget API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchFireTargetData(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des données FIRE.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchFireTargetData();\n  }, []);\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return <p className=\"text-danger\">{error}</p>;\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\n  const progressPercentage = (data.current_net_patrimoine / data.fire_target_amount) * 100;\n\n  return (\n    <div>\n      <h1 className=\"mb-4\">Objectif FIRE</h1>\n      <div className=\"row\">\n        <div className=\"col-md-8\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">Progression vers votre Objectif</h5>\n              <p>\n                Vous avez atteint <strong>{formatCurrency(data.current_net_patrimoine)}</strong> sur votre objectif de <strong>{formatCurrency(data.fire_target_amount)}</strong>.\n              </p>\n              <div className=\"progress\" style={{ height: '25px' }}>\n                <div\n                  className=\"progress-bar progress-bar-striped progress-bar-animated\"\n                  role=\"progressbar\"\n                  style={{ width: `${progressPercentage}%` }}\n                  aria-valuenow={progressPercentage}\n                  aria-valuemin={0}\n                  aria-valuemax={100}\n                >\n                  {progressPercentage.toFixed(2)}%\n                </div>\n              </div>\n              <hr />\n              <p>\n                <strong>Montant restant à investir :</strong> {formatCurrency(data.remaining_to_invest)}\n              </p>\n              <p>\n                <strong>Revenu passif annuel potentiel (SWR {data.secure_withdrawal_rate * 100}%) :</strong> {formatCurrency(data.potential_passive_income)}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FireTarget;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUpC,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGP,QAAQ,CAAwB,IAAI,CAAC;EAC7D,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChCL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEdV,SAAS,CAACc,GAAG,CAAC,cAAc,CAAC,CAC1BC,IAAI,CAACC,QAAQ,IAAI;MAChBV,OAAO,CAACU,QAAQ,CAACX,IAAI,CAAC;MACtBG,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDK,KAAK,CAACR,KAAK,IAAI;MACdS,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAE7C,IAAIE,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAMP,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAC,MAAM;QACLH,QAAQ,CAAC,kDAAkD,CAAC;QAC5DF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDV,SAAS,CAAC,MAAM;IACde,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,OAAO,EAAE,oBAAOL,OAAA;IAAAmB,QAAA,EAAG;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIhB,KAAK,EAAE,oBAAOP,OAAA;IAAGwB,SAAS,EAAC,aAAa;IAAAL,QAAA,EAAEZ;EAAK;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EACxD,IAAI,CAACpB,IAAI,EAAE,oBAAOH,OAAA;IAAAmB,QAAA,EAAG;EAAyB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAME,cAAc,GAAIC,KAAa,IAAK,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAAEC,KAAK,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAC9H,MAAMM,kBAAkB,GAAI7B,IAAI,CAAC8B,sBAAsB,GAAG9B,IAAI,CAAC+B,kBAAkB,GAAI,GAAG;EAExF,oBACElC,OAAA;IAAAmB,QAAA,gBACEnB,OAAA;MAAIwB,SAAS,EAAC,MAAM;MAAAL,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvCvB,OAAA;MAAKwB,SAAS,EAAC,KAAK;MAAAL,QAAA,eAClBnB,OAAA;QAAKwB,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBnB,OAAA;UAAKwB,SAAS,EAAC,MAAM;UAAAL,QAAA,eACnBnB,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAL,QAAA,gBACxBnB,OAAA;cAAIwB,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DvB,OAAA;cAAAmB,QAAA,GAAG,oBACiB,eAAAnB,OAAA;gBAAAmB,QAAA,EAASM,cAAc,CAACtB,IAAI,CAAC8B,sBAAsB;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,2BAAuB,eAAAvB,OAAA;gBAAAmB,QAAA,EAASM,cAAc,CAACtB,IAAI,CAAC+B,kBAAkB;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACnK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvB,OAAA;cAAKwB,SAAS,EAAC,UAAU;cAACK,KAAK,EAAE;gBAAEM,MAAM,EAAE;cAAO,CAAE;cAAAhB,QAAA,eAClDnB,OAAA;gBACEwB,SAAS,EAAC,yDAAyD;gBACnEY,IAAI,EAAC,aAAa;gBAClBP,KAAK,EAAE;kBAAEQ,KAAK,EAAE,GAAGL,kBAAkB;gBAAI,CAAE;gBAC3C,iBAAeA,kBAAmB;gBAClC,iBAAe,CAAE;gBACjB,iBAAe,GAAI;gBAAAb,QAAA,GAElBa,kBAAkB,CAACM,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNvB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAAmB,QAAA,EAAQ;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACE,cAAc,CAACtB,IAAI,CAACoC,mBAAmB,CAAC;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACJvB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAAmB,QAAA,GAAQ,sCAAoC,EAAChB,IAAI,CAACqC,sBAAsB,GAAG,GAAG,EAAC,MAAI;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACE,cAAc,CAACtB,IAAI,CAACsC,wBAAwB,CAAC;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA5EID,UAAoB;AAAAyC,EAAA,GAApBzC,UAAoB;AA8E1B,eAAeA,UAAU;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}