{"ast": null, "code": "import axios from 'axios';\nconst apiClient = axios.create({\n  baseURL: 'http://localhost:8000/api',\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 10000 // 10 second timeout\n});\n\n// Add request interceptor for debugging\napiClient.interceptors.request.use(config => {\n  var _config$method;\n  console.log('API Request:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url, config);\n  return config;\n}, error => {\n  console.error('API Request Error:', error);\n  return Promise.reject(error);\n});\n\n// Add response interceptor for debugging\napiClient.interceptors.response.use(response => {\n  console.log('API Response:', response.status, response.config.url, response.data);\n  return response;\n}, error => {\n  var _error$response, _error$config;\n  console.error('API Response Error:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status, (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url, error);\n  return Promise.reject(error);\n});\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "apiClient", "create", "baseURL", "headers", "timeout", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "data", "_error$response", "_error$config"], "sources": ["D:/GIT/fire_UI/frontend/src/components/ApiClient.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst apiClient = axios.create({\n  baseURL: 'http://localhost:8000/api',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  timeout: 10000, // 10 second timeout\n});\n\n// Add request interceptor for debugging\napiClient.interceptors.request.use(\n  (config) => {\n    console.log('API Request:', config.method?.toUpperCase(), config.url, config);\n    return config;\n  },\n  (error) => {\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor for debugging\napiClient.interceptors.response.use(\n  (response) => {\n    console.log('API Response:', response.status, response.config.url, response.data);\n    return response;\n  },\n  (error) => {\n    console.error('API Response Error:', error.response?.status, error.config?.url, error);\n    return Promise.reject(error);\n  }\n);\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC7BC,OAAO,EAAE,2BAA2B;EACpCC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK,CAAE;AAClB,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,cAAc,GAAAF,cAAA,GAAED,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,EAAEN,MAAM,CAAC;EAC7E,OAAOA,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;EAC1C,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,SAAS,CAACK,YAAY,CAACa,QAAQ,CAACX,GAAG,CAChCW,QAAQ,IAAK;EACZR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACV,MAAM,CAACM,GAAG,EAAEI,QAAQ,CAACE,IAAI,CAAC;EACjF,OAAOF,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAM,eAAA,EAAAC,aAAA;EACTZ,OAAO,CAACK,KAAK,CAAC,qBAAqB,GAAAM,eAAA,GAAEN,KAAK,CAACG,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBF,MAAM,GAAAG,aAAA,GAAEP,KAAK,CAACP,MAAM,cAAAc,aAAA,uBAAZA,aAAA,CAAcR,GAAG,EAAEC,KAAK,CAAC;EACtF,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAef,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}