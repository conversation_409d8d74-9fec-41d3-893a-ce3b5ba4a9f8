import React, { useEffect, useState } from 'react';
import apiClient from './ApiClient';

interface FireTargetData {
  fire_target_amount: number;
  secure_withdrawal_rate: number;
  current_net_patrimoine: number;
  remaining_to_invest: number;
  potential_passive_income: number;
}

const FireTarget: React.FC = () => {
  const [data, setData] = useState<FireTargetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    apiClient.get('/fire-target')
      .then(response => {
        setData(response.data);
        setLoading(false);
      })
      .catch(error => {
        setError('Erreur lors de la récupération des données FIRE.');
        setLoading(false);
        console.error(error);
      });
  }, []);

  if (loading) return <p>Chargement...</p>;
  if (error) return <p className="text-danger">{error}</p>;
  if (!data) return <p>Aucune donnée disponible.</p>;

  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  const progressPercentage = (data.current_net_patrimoine / data.fire_target_amount) * 100;

  return (
    <div>
      <h1 className="mb-4">Objectif FIRE</h1>
      <div className="row">
        <div className="col-md-8">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">Progression vers votre Objectif</h5>
              <p>
                Vous avez atteint <strong>{formatCurrency(data.current_net_patrimoine)}</strong> sur votre objectif de <strong>{formatCurrency(data.fire_target_amount)}</strong>.
              </p>
              <div className="progress" style={{ height: '25px' }}>
                <div
                  className="progress-bar progress-bar-striped progress-bar-animated"
                  role="progressbar"
                  style={{ width: `${progressPercentage}%` }}
                  aria-valuenow={progressPercentage}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  {progressPercentage.toFixed(2)}%
                </div>
              </div>
              <hr />
              <p>
                <strong>Montant restant à investir :</strong> {formatCurrency(data.remaining_to_invest)}
              </p>
              <p>
                <strong>Revenu passif annuel potentiel (SWR {data.secure_withdrawal_rate * 100}%) :</strong> {formatCurrency(data.potential_passive_income)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FireTarget;