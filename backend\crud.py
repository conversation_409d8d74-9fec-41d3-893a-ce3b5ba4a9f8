from sqlalchemy.orm import Session, joinedload
import models, schemas

# Asset CRUD
def get_asset(db: Session, asset_id: int):
    return db.query(models.Asset).options(joinedload(models.Asset.categories).joinedload(models.AssetCategory.category)).filter(models.Asset.id == asset_id).first()

def get_assets(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Asset).options(joinedload(models.Asset.categories).joinedload(models.AssetCategory.category)).offset(skip).limit(limit).all()

def create_asset(db: Session, asset: schemas.AssetCreate):
    db_asset = models.Asset(name=asset.name, value=asset.value, annual_interest=asset.annual_interest, notes=asset.notes, update_date=asset.update_date)
    db.add(db_asset)
    db.flush() # Flush to get db_asset.id

    for category_value in asset.categories:
        category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
        if not category:
            category = models.Category(name=category_value.name)
            db.add(category)
            db.flush() # Flush to get category.id

        asset_category = models.AssetCategory(asset_id=db_asset.id, category_id=category.id, value=category_value.value)
        db.add(asset_category)
    
    db.commit()
    db.refresh(db_asset)
    return db_asset

def update_asset(db: Session, asset_id: int, asset: schemas.AssetCreate):
    db_asset = get_asset(db, asset_id)
    if db_asset:
        db_asset.name = asset.name
        db_asset.value = asset.value
        db_asset.annual_interest = asset.annual_interest
        db_asset.update_date = asset.update_date
        
        # Clear existing categories and add new ones
        for ac in db_asset.categories:
            db.delete(ac)
        db.flush()

        for category_value in asset.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush()

            asset_category = models.AssetCategory(asset_id=db_asset.id, category_id=category.id, value=category_value.value)
            db.add(asset_category)
            
        db.commit()
        db.refresh(db_asset)
    return db_asset

def delete_asset(db: Session, asset_id: int):
    db_asset = get_asset(db, asset_id)
    if db_asset:
        db.delete(db_asset)
        db.commit()
    return db_asset

# Liability CRUD
def get_liability(db: Session, liability_id: int):
    return db.query(models.Liability).filter(models.Liability.id == liability_id).first()

def get_liabilities(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Liability).offset(skip).limit(limit).all()

def create_liability(db: Session, liability: schemas.LiabilityCreate):
    db_liability = models.Liability(**liability.model_dump())
    db.add(db_liability)
    db.commit()
    db.refresh(db_liability)
    return db_liability

def update_liability(db: Session, liability_id: int, liability: schemas.LiabilityCreate):
    db_liability = get_liability(db, liability_id)
    if db_liability:
        for key, value in liability.model_dump().items():
            setattr(db_liability, key, value)
        db.commit()
        db.refresh(db_liability)
    return db_liability

def delete_liability(db: Session, liability_id: int):
    db_liability = get_liability(db, liability_id)
    if db_liability:
        db.delete(db_liability)
        db.commit()
    return db_liability

# PatrimoineHistory CRUD
def get_patrimoine_history(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.PatrimoineHistory).offset(skip).limit(limit).all()

def create_patrimoine_history(db: Session, history: schemas.PatrimoineHistoryCreate):
    db_history = models.PatrimoineHistory(**history.model_dump())
    db.add(db_history)
    db.commit()
    db.refresh(db_history)
    return db_history

# SCPI CRUD
def get_scpi(db: Session, scpi_id: int):
    return db.query(models.SCPI).filter(models.SCPI.id == scpi_id).first()

def get_scpis(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.SCPI).offset(skip).limit(limit).all()

def create_scpi(db: Session, scpi: schemas.SCPICreate):
    db_scpi = models.SCPI(**scpi.model_dump())
    db.add(db_scpi)
    db.commit()
    db.refresh(db_scpi)
    return db_scpi

def update_scpi(db: Session, scpi_id: int, scpi: schemas.SCPICreate):
    db_scpi = db.query(models.SCPI).filter(models.SCPI.id == scpi_id).first()
    if db_scpi:
        for key, value in scpi.model_dump().items():
            setattr(db_scpi, key, value)
        db.commit()
        db.refresh(db_scpi)
    return db_scpi

def delete_scpi(db: Session, scpi_id: int):
    db_scpi = db.query(models.SCPI).filter(models.SCPI.id == scpi_id).first()
    if db_scpi:
        db.delete(db_scpi)
        db.commit()
    return db_scpi

# Fire Settings CRUD
def get_fire_settings(db: Session):
    return db.query(models.FireSettings).first()

def create_fire_settings(db: Session, settings: schemas.FireSettingsCreate):
    db_settings = models.FireSettings(**settings.model_dump())
    db.add(db_settings)
    db.commit()
    db.refresh(db_settings)
    return db_settings

def update_fire_settings(db: Session, settings: schemas.FireSettingsUpdate):
    db_settings = db.query(models.FireSettings).first()
    if db_settings:
        for key, value in settings.model_dump(exclude_unset=True).items():
            setattr(db_settings, key, value)
        db.commit()
        db.refresh(db_settings)
    return db_settings

# Patrimoine Evolution CRUD
def get_patrimoine_evolution(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.PatrimoineEvolution).order_by(models.PatrimoineEvolution.annee).offset(skip).limit(limit).all()

def get_patrimoine_evolution_by_year(db: Session, annee: int):
    return db.query(models.PatrimoineEvolution).filter(models.PatrimoineEvolution.annee == annee).first()

def create_patrimoine_evolution(db: Session, evolution: schemas.PatrimoineEvolutionCreate):
    db_evolution = models.PatrimoineEvolution(**evolution.model_dump())
    db.add(db_evolution)
    db.commit()
    db.refresh(db_evolution)
    return db_evolution

def update_patrimoine_evolution(db: Session, annee: int, evolution: schemas.PatrimoineEvolutionUpdate):
    db_evolution = db.query(models.PatrimoineEvolution).filter(models.PatrimoineEvolution.annee == annee).first()
    if db_evolution:
        for key, value in evolution.model_dump(exclude_unset=True).items():
            setattr(db_evolution, key, value)
        db.commit()
        db.refresh(db_evolution)
    return db_evolution

def delete_patrimoine_evolution(db: Session, annee: int):
    db_evolution = db.query(models.PatrimoineEvolution).filter(models.PatrimoineEvolution.annee == annee).first()
    if db_evolution:
        db.delete(db_evolution)
        db.commit()
    return db_evolution

def calculate_evolution_metrics(db: Session):
    """Calcule les métriques d'évolution pour toutes les années"""
    evolutions = db.query(models.PatrimoineEvolution).order_by(models.PatrimoineEvolution.annee).all()

    if not evolutions:
        return

    base_year = evolutions[0]

    for i, evolution in enumerate(evolutions):
        if i == 0:
            # Première année : pas d'évolution
            evolution.evolution_pourcentage = 0
            evolution.evolution_euros = 0
            evolution.croissance_moyenne = 0
            evolution.tcam = 0
        else:
            # Évolution par rapport à l'année précédente
            prev_evolution = evolutions[i-1]
            evolution.evolution_euros = evolution.total_patrimoine - prev_evolution.total_patrimoine
            evolution.evolution_pourcentage = (evolution.evolution_euros / prev_evolution.total_patrimoine) * 100 if prev_evolution.total_patrimoine > 0 else 0

            # TCAM depuis 2015
            years_diff = evolution.annee - base_year.annee
            if years_diff > 0 and base_year.total_patrimoine > 0:
                evolution.tcam = (pow(evolution.total_patrimoine / base_year.total_patrimoine, 1/years_diff) - 1) * 100
                evolution.croissance_moyenne = ((evolution.total_patrimoine - base_year.total_patrimoine) / years_diff)
            else:
                evolution.tcam = 0
                evolution.croissance_moyenne = 0

    db.commit()
    return evolutions

# Budget Categories CRUD
def get_budget_categories(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.BudgetCategory).order_by(models.BudgetCategory.ordre_affichage, models.BudgetCategory.nom).offset(skip).limit(limit).all()

def get_budget_category(db: Session, category_id: int):
    return db.query(models.BudgetCategory).filter(models.BudgetCategory.id == category_id).first()

def get_budget_category_by_name(db: Session, nom: str):
    return db.query(models.BudgetCategory).filter(models.BudgetCategory.nom == nom).first()

def create_budget_category(db: Session, category: schemas.BudgetCategoryCreate):
    db_category = models.BudgetCategory(**category.model_dump())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

def update_budget_category(db: Session, category_id: int, category: schemas.BudgetCategoryUpdate):
    db_category = db.query(models.BudgetCategory).filter(models.BudgetCategory.id == category_id).first()
    if db_category:
        for key, value in category.model_dump(exclude_unset=True).items():
            setattr(db_category, key, value)
        db.commit()
        db.refresh(db_category)
    return db_category

def delete_budget_category(db: Session, category_id: int):
    db_category = db.query(models.BudgetCategory).filter(models.BudgetCategory.id == category_id).first()
    if db_category:
        # Vérifier s'il y a des dépenses associées
        existing_expenses = db.query(models.DepenseReelle).filter(models.DepenseReelle.categorie_id == category_id).first()
        if existing_expenses:
            raise ValueError(f"Cannot delete category '{db_category.nom}' because it has associated expenses. Delete all expenses first.")

        db.delete(db_category)
        db.commit()
    return db_category

# Dépenses Réelles CRUD
def get_depenses_reelles(db: Session, skip: int = 0, limit: int = 100, annee: int = None, mois: int = None):
    query = db.query(models.DepenseReelle)
    if annee:
        query = query.filter(models.DepenseReelle.annee == annee)
    if mois:
        query = query.filter(models.DepenseReelle.mois == mois)
    return query.order_by(models.DepenseReelle.date_depense.desc()).offset(skip).limit(limit).all()

def get_depense_reelle(db: Session, depense_id: int):
    return db.query(models.DepenseReelle).filter(models.DepenseReelle.id == depense_id).first()

def create_depense_reelle(db: Session, depense: schemas.DepenseReelleCreate):
    # Extraire mois et année de la date
    mois = depense.date_depense.month
    annee = depense.date_depense.year

    db_depense = models.DepenseReelle(
        **depense.model_dump(),
        mois=mois,
        annee=annee
    )
    db.add(db_depense)
    db.commit()
    db.refresh(db_depense)
    return db_depense

def update_depense_reelle(db: Session, depense_id: int, depense: schemas.DepenseReelleUpdate):
    db_depense = db.query(models.DepenseReelle).filter(models.DepenseReelle.id == depense_id).first()
    if db_depense:
        update_data = depense.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_depense, key, value)

        # Mettre à jour mois et année si la date change
        if 'date_depense' in update_data:
            db_depense.mois = db_depense.date_depense.month
            db_depense.annee = db_depense.date_depense.year

        db.commit()
        db.refresh(db_depense)
    return db_depense

def delete_depense_reelle(db: Session, depense_id: int):
    db_depense = db.query(models.DepenseReelle).filter(models.DepenseReelle.id == depense_id).first()
    if db_depense:
        db.delete(db_depense)
        db.commit()
    return db_depense

# Analyses budgétaires
def get_budget_analysis(db: Session, annee: int = None):
    """Analyse complète du budget vs réalisé"""
    from datetime import datetime
    from sqlalchemy import func

    if not annee:
        annee = datetime.now().year

    categories = db.query(models.BudgetCategory).order_by(models.BudgetCategory.ordre_affichage, models.BudgetCategory.nom).all()
    analyses = []

    for category in categories:
        # Dépenses de l'année
        depenses_annee = db.query(func.sum(models.DepenseReelle.montant)).filter(
            models.DepenseReelle.categorie_id == category.id,
            models.DepenseReelle.annee == annee
        ).scalar() or 0

        # Dépenses du mois actuel
        mois_actuel = datetime.now().month
        depenses_mois = db.query(func.sum(models.DepenseReelle.montant)).filter(
            models.DepenseReelle.categorie_id == category.id,
            models.DepenseReelle.annee == annee,
            models.DepenseReelle.mois == mois_actuel
        ).scalar() or 0

        # Calculs
        ecart_euros = depenses_annee - category.budget_annuel
        ecart_pourcentage = (ecart_euros / category.budget_annuel * 100) if category.budget_annuel > 0 else 0

        # Projection annuelle basée sur les dépenses actuelles
        mois_ecoules = mois_actuel
        projection_annuelle = (depenses_annee / mois_ecoules * 12) if mois_ecoules > 0 else 0

        # Statut
        if ecart_pourcentage <= -5:
            statut = "excellent"  # Bien sous budget
        elif ecart_pourcentage <= 5:
            statut = "ok"  # Dans la cible
        elif ecart_pourcentage <= 15:
            statut = "attention"  # Proche de la limite
        else:
            statut = "depassement"  # Dépassement

        analyses.append(schemas.BudgetAnalysis(
            categorie_id=category.id,
            nom_categorie=category.nom,
            budget_annuel=category.budget_annuel,
            depenses_mois_actuel=depenses_mois,
            depenses_annee_actuelle=depenses_annee,
            ecart_euros=ecart_euros,
            ecart_pourcentage=ecart_pourcentage,
            projection_annuelle=projection_annuelle,
            statut=statut
        ))

    return analyses

def get_budget_summary(db: Session, annee: int = None):
    """Résumé global du budget pour FIRE"""
    from sqlalchemy import func

    # Total budget annuel
    total_budget = db.query(func.sum(models.BudgetCategory.budget_annuel)).scalar() or 0

    # Objectif FIRE documenté (basé sur votre plan FIRE)
    FIRE_TARGET_DOCUMENTED = 910150  # Objectif documenté dans le plan FIRE
    WITHDRAWAL_RATE_DOCUMENTED = 36406  # Retrait annuel brut documenté

    # Calcul du retrait brut basé sur le budget actuel
    # Si le budget correspond au plan (25 484€), utiliser le retrait documenté
    # Sinon, ajuster proportionnellement
    if abs(total_budget - 25484) < 100:  # Tolérance de 100€
        retrait_brut = WITHDRAWAL_RATE_DOCUMENTED
        capital_fire = FIRE_TARGET_DOCUMENTED
    else:
        # Ajustement proportionnel si le budget a été modifié
        ratio = total_budget / 25484
        retrait_brut = WITHDRAWAL_RATE_DOCUMENTED * ratio
        capital_fire = retrait_brut * 25

    # Message d'information FIRE
    if capital_fire == FIRE_TARGET_DOCUMENTED:
        impact_fire = f"Objectif FIRE documenté: {capital_fire:,.0f}€ (retrait {retrait_brut:,.0f}€/an)"
    else:
        impact_fire = f"Capital FIRE ajusté: {capital_fire:,.0f}€ (budget modifié vs plan documenté)"

    return schemas.BudgetSummary(
        total_budget_annuel=total_budget,
        retrait_brut_necessaire=retrait_brut,
        impact_date_fire=impact_fire
    )