{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(ArcElement, Tooltip, Legend);\nconst Dashboard = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    console.log('Dashboard: Starting API call to /dashboard');\n    apiClient.get('/dashboard').then(response => {\n      console.log('Dashboard: API call successful', response.data);\n      setData(response.data);\n      setLoading(false);\n    }).catch(error => {\n      var _error$response, _error$response2, _error$response3;\n      console.error('Dashboard: API call failed', error);\n      console.error('Dashboard: Error details', {\n        message: error.message,\n        response: error.response,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data\n      });\n      setError('Erreur lors de la récupération des données du dashboard.');\n      setLoading(false);\n    });\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"text-danger\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 21\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 21\n  }, this);\n  const chartData = {\n    labels: Object.keys(data.allocation),\n    datasets: [{\n      label: 'Répartition du portefeuille',\n      data: Object.values(data.allocation),\n      backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)'],\n      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],\n      borderWidth: 1\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"mb-4\",\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-primary mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Patrimoine Net Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.net_patrimoine)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-success mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Actifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.total_assets)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-danger mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Passifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.total_liabilities)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"R\\xE9partition des Actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Doughnut, {\n          data: chartData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"RiL7vLwmC7ZWXKL/bXt2EIBjBYk=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Doughnut", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "console", "log", "get", "then", "response", "catch", "_error$response", "_error$response2", "_error$response3", "message", "status", "statusText", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "chartData", "labels", "Object", "keys", "allocation", "datasets", "label", "values", "backgroundColor", "borderColor", "borderWidth", "Intl", "NumberFormat", "style", "currency", "format", "net_patrimoine", "total_assets", "total_liabilities", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\n\nChartJS.register(ArcE<PERSON>, Tooltip, Legend);\n\ninterface DashboardData {\n  net_patrimoine: number;\n  total_assets: number;\n  total_liabilities: number;\n  allocation: { [key: string]: number };\n}\n\nconst Dashboard: React.FC = () => {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    console.log('Dashboard: Starting API call to /dashboard');\n    apiClient.get('/dashboard')\n      .then(response => {\n        console.log('Dashboard: API call successful', response.data);\n        setData(response.data);\n        setLoading(false);\n      })\n      .catch(error => {\n        console.error('Dashboard: API call failed', error);\n        console.error('Dashboard: Error details', {\n          message: error.message,\n          response: error.response,\n          status: error.response?.status,\n          statusText: error.response?.statusText,\n          data: error.response?.data\n        });\n        setError('Erreur lors de la récupération des données du dashboard.');\n        setLoading(false);\n      });\n  }, []);\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return <p className=\"text-danger\">{error}</p>;\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const chartData = {\n    labels: Object.keys(data.allocation),\n    datasets: [\n      {\n        label: 'Répartition du portefeuille',\n        data: Object.values(data.allocation),\n        backgroundColor: [\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n          'rgba(75, 192, 192, 0.7)',\n          'rgba(153, 102, 255, 0.7)',\n          'rgba(255, 159, 64, 0.7)',\n        ],\n        borderColor: [\n          'rgba(255, 99, 132, 1)',\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 206, 86, 1)',\n          'rgba(75, 192, 192, 1)',\n          'rgba(153, 102, 255, 1)',\n          'rgba(255, 159, 64, 1)',\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  return (\n    <div>\n      <h1 className=\"mb-4\">Dashboard</h1>\n      <div className=\"row\">\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-primary mb-3\">\n            <div className=\"card-header\">Patrimoine Net Total</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.net_patrimoine)}</h5>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-success mb-3\">\n            <div className=\"card-header\">Total Actifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_assets)}</h5>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-danger mb-3\">\n            <div className=\"card-header\">Total Passifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_liabilities)}</h5>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"row mt-4\">\n        <div className=\"col-md-6\">\n          <h2>Répartition des Actifs</h2>\n          <Doughnut data={chartData} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,KAAK,IAAIC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzEL,OAAO,CAACM,QAAQ,CAACL,UAAU,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAS7C,MAAMI,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACdoB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzDnB,SAAS,CAACoB,GAAG,CAAC,YAAY,CAAC,CACxBC,IAAI,CAACC,QAAQ,IAAI;MAChBJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEG,QAAQ,CAACV,IAAI,CAAC;MAC5DC,OAAO,CAACS,QAAQ,CAACV,IAAI,CAAC;MACtBG,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDQ,KAAK,CAACP,KAAK,IAAI;MAAA,IAAAQ,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdR,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDE,OAAO,CAACF,KAAK,CAAC,0BAA0B,EAAE;QACxCW,OAAO,EAAEX,KAAK,CAACW,OAAO;QACtBL,QAAQ,EAAEN,KAAK,CAACM,QAAQ;QACxBM,MAAM,GAAAJ,eAAA,GAAER,KAAK,CAACM,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBI,MAAM;QAC9BC,UAAU,GAAAJ,gBAAA,GAAET,KAAK,CAACM,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBI,UAAU;QACtCjB,IAAI,GAAAc,gBAAA,GAAEV,KAAK,CAACM,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBd;MACxB,CAAC,CAAC;MACFK,QAAQ,CAAC,0DAA0D,CAAC;MACpEF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,EAAE,oBAAON,OAAA;IAAAsB,QAAA,EAAG;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIlB,KAAK,EAAE,oBAAOR,OAAA;IAAG2B,SAAS,EAAC,aAAa;IAAAL,QAAA,EAAEd;EAAK;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EACxD,IAAI,CAACtB,IAAI,EAAE,oBAAOJ,OAAA;IAAAsB,QAAA,EAAG;EAAyB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAME,SAAS,GAAG;IAChBC,MAAM,EAAEC,MAAM,CAACC,IAAI,CAAC3B,IAAI,CAAC4B,UAAU,CAAC;IACpCC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,6BAA6B;MACpC9B,IAAI,EAAE0B,MAAM,CAACK,MAAM,CAAC/B,IAAI,CAAC4B,UAAU,CAAC;MACpCI,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,CAC1B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,CACxB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,oBACEtC,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAI2B,SAAS,EAAC,MAAM;MAAAL,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnC1B,OAAA;MAAK2B,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClBtB,OAAA;QAAK2B,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBtB,OAAA;UAAK2B,SAAS,EAAC,iCAAiC;UAAAL,QAAA,gBAC9CtB,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvD1B,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBtB,OAAA;cAAI2B,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIiB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAACvC,IAAI,CAACwC,cAAc;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1B,OAAA;QAAK2B,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBtB,OAAA;UAAK2B,SAAS,EAAC,iCAAiC;UAAAL,QAAA,gBAC9CtB,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/C1B,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBtB,OAAA;cAAI2B,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIiB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAACvC,IAAI,CAACyC,YAAY;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1B,OAAA;QAAK2B,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBtB,OAAA;UAAK2B,SAAS,EAAC,gCAAgC;UAAAL,QAAA,gBAC7CtB,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD1B,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBtB,OAAA;cAAI2B,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIiB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAACvC,IAAI,CAAC0C,iBAAiB;YAAC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN1B,OAAA;MAAK2B,SAAS,EAAC,UAAU;MAAAL,QAAA,eACvBtB,OAAA;QAAK2B,SAAS,EAAC,UAAU;QAAAL,QAAA,gBACvBtB,OAAA;UAAAsB,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/B1B,OAAA,CAACP,QAAQ;UAACW,IAAI,EAAEwB;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA/FID,SAAmB;AAAA6C,EAAA,GAAnB7C,SAAmB;AAiGzB,eAAeA,SAAS;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}