import datetime
from sqlalchemy.orm import Session
from database import SessionLocal
import models

def seed_fire_settings():
    db = SessionLocal()
    
    # Check if settings already exist
    existing_settings = db.query(models.FireSettings).first()
    if existing_settings:
        print("Fire settings already exist")
        db.close()
        return

    # Create default FIRE settings
    fire_settings = models.FireSettings(
        fire_target_amount=910150,
        secure_withdrawal_rate=0.04,
        update_date=datetime.date(2025, 7, 1)
    )

    db.add(fire_settings)
    db.commit()
    db.refresh(fire_settings)

    print("Default FIRE settings created:")
    print(f"- Target amount: {fire_settings.fire_target_amount:,.2f} €")
    print(f"- SWR: {fire_settings.secure_withdrawal_rate:.1%}")

    db.close()

if __name__ == "__main__":
    seed_fire_settings()
