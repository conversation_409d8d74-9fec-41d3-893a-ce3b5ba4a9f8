{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\SCPI.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SCPIForm = ({\n  scpi,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: (scpi === null || scpi === void 0 ? void 0 : scpi.name) || '',\n    price_per_share: (scpi === null || scpi === void 0 ? void 0 : scpi.price_per_share) || 0,\n    number_of_shares: (scpi === null || scpi === void 0 ? void 0 : scpi.number_of_shares) || 0,\n    total_value: (scpi === null || scpi === void 0 ? void 0 : scpi.total_value) || 0,\n    update_date: scpi !== null && scpi !== void 0 && scpi.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n  });\n\n  // Auto-calculate total_value when price or shares change\n  useEffect(() => {\n    const calculatedTotal = formData.price_per_share * formData.number_of_shares;\n    if (calculatedTotal !== formData.total_value) {\n      setFormData(prev => ({\n        ...prev,\n        total_value: calculatedTotal\n      }));\n    }\n  }, [formData.price_per_share, formData.number_of_shares]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const method = scpi !== null && scpi !== void 0 && scpi.id ? 'put' : 'post';\n    const url = scpi !== null && scpi !== void 0 && scpi.id ? `/scpi/${scpi.id}` : '/scpi';\n    console.log('Submitting SCPI:', {\n      method,\n      url,\n      formData\n    });\n    try {\n      const response = await apiClient[method](url, formData);\n      console.log('SCPI saved successfully:', response.data);\n      onSave();\n    } catch (error) {\n      var _error$response;\n      console.error(\"Erreur lors de la sauvegarde de la SCPI\", error);\n      console.error(\"Error details:\", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Nom de la SCPI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        value: formData.name,\n        onChange: e => setFormData({\n          ...formData,\n          name: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Prix par part (\\u20AC)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"0.01\",\n        value: formData.price_per_share,\n        onChange: e => setFormData({\n          ...formData,\n          price_per_share: parseFloat(e.target.value) || 0\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Nombre de parts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.number_of_shares,\n        onChange: e => setFormData({\n          ...formData,\n          number_of_shares: parseInt(e.target.value) || 0\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Valeur totale (\\u20AC)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"0.01\",\n        value: formData.total_value,\n        onChange: e => setFormData({\n          ...formData,\n          total_value: parseFloat(e.target.value) || 0\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n        className: \"text-muted\",\n        children: [\"Calcul\\xE9 automatiquement : \", (formData.price_per_share * formData.number_of_shares).toFixed(2), \" \\u20AC\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Date de mise \\xE0 jour\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"date\",\n        value: formData.update_date,\n        onChange: e => setFormData({\n          ...formData,\n          update_date: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-end gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onCancel,\n        children: \"Annuler\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        type: \"submit\",\n        children: \"Sauvegarder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 9\n  }, this);\n};\n_s(SCPIForm, \"yLDHUhRx9LG19hgYQa6vhlthGas=\");\n_c = SCPIForm;\nconst SCPI = () => {\n  _s2();\n  const [scpis, setSCPIs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedSCPI, setSelectedSCPI] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchSCPIs = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/scpi').then(response => {\n      setSCPIs(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('SCPI API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchSCPIs(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des SCPI.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchSCPIs();\n  }, []);\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedSCPI(null);\n    fetchSCPIs();\n  };\n  const handleDelete = async id => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cette SCPI ?\")) {\n      try {\n        await apiClient.delete(`/scpi/${id}`);\n        fetchSCPIs();\n      } catch (error) {\n        console.error(\"Erreur lors de la suppression de la SCPI\", error);\n      }\n    }\n  };\n  if (loading && !showModal) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 37\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchSCPIs,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n  const totalSCPI = scpis.reduce((sum, scpi) => sum + scpi.total_value, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Mes SCPI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => {\n          setSelectedSCPI({});\n          setShowModal(true);\n        },\n        children: \"Ajouter une SCPI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table table-striped table-hover\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-dark\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Nom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Prix par part\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Nombre de parts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Valeur totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Date de mise \\xE0 jour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: scpis.map(scpi => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: scpi.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(scpi.price_per_share)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: scpi.number_of_shares\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(scpi.total_value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: new Date(scpi.update_date).toLocaleDateString('fr-FR')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              onClick: () => {\n                setSelectedSCPI(scpi);\n                setShowModal(true);\n              },\n              children: \"Modifier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: () => handleDelete(scpi.id),\n              children: \"Supprimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, scpi.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tfoot\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          className: \"table-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            colSpan: 3,\n            children: \"TOTAL SCPI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(totalSCPI)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            colSpan: 2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [selectedSCPI !== null && selectedSCPI !== void 0 && selectedSCPI.id ? 'Modifier' : 'Ajouter', \" une SCPI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(SCPIForm, {\n          scpi: selectedSCPI,\n          onSave: handleSave,\n          onCancel: () => setShowModal(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s2(SCPI, \"uVgspR3vga2j8nfkOMpNZcdOI/4=\");\n_c2 = SCPI;\nexport default SCPI;\nvar _c, _c2;\n$RefreshReg$(_c, \"SCPIForm\");\n$RefreshReg$(_c2, \"SCPI\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Modal", "<PERSON><PERSON>", "Form", "jsxDEV", "_jsxDEV", "SCPIForm", "scpi", "onSave", "onCancel", "_s", "formData", "setFormData", "name", "price_per_share", "number_of_shares", "total_value", "update_date", "Date", "toISOString", "split", "calculatedTotal", "prev", "handleSubmit", "e", "preventDefault", "method", "id", "url", "console", "log", "response", "data", "error", "_error$response", "onSubmit", "children", "Group", "className", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "type", "value", "onChange", "target", "required", "step", "parseFloat", "parseInt", "Text", "toFixed", "variant", "onClick", "_c", "SCPI", "_s2", "scpis", "setSCPIs", "loading", "setLoading", "setError", "showModal", "setShowModal", "selectedSCPI", "setSelectedSCPI", "retryCount", "setRetryCount", "fetchSCPIs", "get", "then", "catch", "setTimeout", "handleSave", "handleDelete", "window", "confirm", "delete", "totalSCPI", "reduce", "sum", "map", "Intl", "NumberFormat", "style", "currency", "format", "toLocaleDateString", "size", "colSpan", "show", "onHide", "Header", "closeButton", "Title", "Body", "_c2", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/SCPI.tsx"], "sourcesContent": ["import React, { useEffect, useState, FormEvent } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form } from 'react-bootstrap';\n\ninterface SCPI {\n  id: number;\n  name: string;\n  price_per_share: number;\n  number_of_shares: number;\n  total_value: number;\n  update_date: string;\n}\n\nconst SCPIForm: React.FC<{ scpi: Partial<SCPI> | null, onSave: () => void, onCancel: () => void }> = ({ scpi, onSave, onCancel }) => {\n    const [formData, setFormData] = useState({\n        name: scpi?.name || '',\n        price_per_share: scpi?.price_per_share || 0,\n        number_of_shares: scpi?.number_of_shares || 0,\n        total_value: scpi?.total_value || 0,\n        update_date: scpi?.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n    });\n\n    // Auto-calculate total_value when price or shares change\n    useEffect(() => {\n        const calculatedTotal = formData.price_per_share * formData.number_of_shares;\n        if (calculatedTotal !== formData.total_value) {\n            setFormData(prev => ({ ...prev, total_value: calculatedTotal }));\n        }\n    }, [formData.price_per_share, formData.number_of_shares]);\n\n    const handleSubmit = async (e: FormEvent) => {\n        e.preventDefault();\n        const method = scpi?.id ? 'put' : 'post';\n        const url = scpi?.id ? `/scpi/${scpi.id}` : '/scpi';\n        \n        console.log('Submitting SCPI:', { method, url, formData });\n        \n        try {\n            const response = await apiClient[method](url, formData);\n            console.log('SCPI saved successfully:', response.data);\n            onSave();\n        } catch (error: any) {\n            console.error(\"Erreur lors de la sauvegarde de la SCPI\", error);\n            console.error(\"Error details:\", error.response?.data);\n        }\n    };\n\n    return (\n        <Form onSubmit={handleSubmit}>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Nom de la SCPI</Form.Label>\n                <Form.Control\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                    required\n                />\n            </Form.Group>\n            \n            <Form.Group className=\"mb-3\">\n                <Form.Label>Prix par part (€)</Form.Label>\n                <Form.Control\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={formData.price_per_share}\n                    onChange={(e) => setFormData({ ...formData, price_per_share: parseFloat(e.target.value) || 0 })}\n                    required\n                />\n            </Form.Group>\n            \n            <Form.Group className=\"mb-3\">\n                <Form.Label>Nombre de parts</Form.Label>\n                <Form.Control\n                    type=\"number\"\n                    value={formData.number_of_shares}\n                    onChange={(e) => setFormData({ ...formData, number_of_shares: parseInt(e.target.value) || 0 })}\n                    required\n                />\n            </Form.Group>\n            \n            <Form.Group className=\"mb-3\">\n                <Form.Label>Valeur totale (€)</Form.Label>\n                <Form.Control\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={formData.total_value}\n                    onChange={(e) => setFormData({ ...formData, total_value: parseFloat(e.target.value) || 0 })}\n                    required\n                />\n                <Form.Text className=\"text-muted\">\n                    Calculé automatiquement : {(formData.price_per_share * formData.number_of_shares).toFixed(2)} €\n                </Form.Text>\n            </Form.Group>\n            \n            <Form.Group className=\"mb-3\">\n                <Form.Label>Date de mise à jour</Form.Label>\n                <Form.Control\n                    type=\"date\"\n                    value={formData.update_date}\n                    onChange={(e) => setFormData({ ...formData, update_date: e.target.value })}\n                    required\n                />\n            </Form.Group>\n            \n            <div className=\"d-flex justify-content-end gap-2\">\n                <Button variant=\"secondary\" onClick={onCancel}>Annuler</Button>\n                <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\n            </div>\n        </Form>\n    );\n};\n\nconst SCPI: React.FC = () => {\n  const [scpis, setSCPIs] = useState<SCPI[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedSCPI, setSelectedSCPI] = useState<Partial<SCPI> | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchSCPIs = () => {\n    setLoading(true);\n    setError(null);\n    \n    apiClient.get('/scpi')\n      .then(response => {\n        setSCPIs(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('SCPI API error:', error);\n        \n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchSCPIs(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des SCPI.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchSCPIs();\n  }, []);\n\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedSCPI(null);\n    fetchSCPIs();\n  };\n\n  const handleDelete = async (id: number) => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cette SCPI ?\")) {\n        try {\n            await apiClient.delete(`/scpi/${id}`);\n            fetchSCPIs();\n        } catch (error) {\n            console.error(\"Erreur lors de la suppression de la SCPI\", error);\n        }\n    }\n  };\n\n  if (loading && !showModal) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchSCPIs}>\n        Réessayer\n      </button>\n    </div>\n  );\n\n  const totalSCPI = scpis.reduce((sum, scpi) => sum + scpi.total_value, 0);\n\n  return (\n    <div>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1>Mes SCPI</h1>\n        <Button variant=\"primary\" onClick={() => { setSelectedSCPI({}); setShowModal(true); }}>Ajouter une SCPI</Button>\n      </div>\n      \n      <table className=\"table table-striped table-hover\">\n        <thead className=\"table-dark\">\n          <tr>\n            <th>Nom</th>\n            <th className=\"text-end\">Prix par part</th>\n            <th className=\"text-end\">Nombre de parts</th>\n            <th className=\"text-end\">Valeur totale</th>\n            <th className=\"text-center\">Date de mise à jour</th>\n            <th className=\"text-center\">Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          {scpis.map(scpi => (\n            <tr key={scpi.id}>\n              <td>{scpi.name}</td>\n              <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpi.price_per_share)}</td>\n              <td className=\"text-end\">{scpi.number_of_shares}</td>\n              <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpi.total_value)}</td>\n              <td className=\"text-center\">{new Date(scpi.update_date).toLocaleDateString('fr-FR')}</td>\n              <td className=\"text-center\">\n                <Button \n                  variant=\"outline-primary\" \n                  size=\"sm\" \n                  className=\"me-2\"\n                  onClick={() => { setSelectedSCPI(scpi); setShowModal(true); }}\n                >\n                  Modifier\n                </Button>\n                <Button \n                  variant=\"outline-danger\" \n                  size=\"sm\"\n                  onClick={() => handleDelete(scpi.id)}\n                >\n                  Supprimer\n                </Button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n        <tfoot>\n          <tr className=\"table-info\">\n            <th colSpan={3}>TOTAL SCPI</th>\n            <th className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalSCPI)}</th>\n            <th colSpan={2}></th>\n          </tr>\n        </tfoot>\n      </table>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>{selectedSCPI?.id ? 'Modifier' : 'Ajouter'} une SCPI</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <SCPIForm \n            scpi={selectedSCPI} \n            onSave={handleSave} \n            onCancel={() => setShowModal(false)} \n          />\n        </Modal.Body>\n      </Modal>\n    </div>\n  );\n};\n\nexport default SCPI;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAC7D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWtD,MAAMC,QAA4F,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjI,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACrCc,IAAI,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,KAAI,EAAE;IACtBC,eAAe,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,eAAe,KAAI,CAAC;IAC3CC,gBAAgB,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,gBAAgB,KAAI,CAAC;IAC7CC,WAAW,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,WAAW,KAAI,CAAC;IACnCC,WAAW,EAAEV,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEU,WAAW,GAAG,IAAIC,IAAI,CAACX,IAAI,CAACU,WAAW,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACnI,CAAC,CAAC;;EAEF;EACAtB,SAAS,CAAC,MAAM;IACZ,MAAMuB,eAAe,GAAGV,QAAQ,CAACG,eAAe,GAAGH,QAAQ,CAACI,gBAAgB;IAC5E,IAAIM,eAAe,KAAKV,QAAQ,CAACK,WAAW,EAAE;MAC1CJ,WAAW,CAACU,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEN,WAAW,EAAEK;MAAgB,CAAC,CAAC,CAAC;IACpE;EACJ,CAAC,EAAE,CAACV,QAAQ,CAACG,eAAe,EAAEH,QAAQ,CAACI,gBAAgB,CAAC,CAAC;EAEzD,MAAMQ,YAAY,GAAG,MAAOC,CAAY,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAGnB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,EAAE,GAAG,KAAK,GAAG,MAAM;IACxC,MAAMC,GAAG,GAAGrB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,EAAE,GAAG,SAASpB,IAAI,CAACoB,EAAE,EAAE,GAAG,OAAO;IAEnDE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAAEJ,MAAM;MAAEE,GAAG;MAAEjB;IAAS,CAAC,CAAC;IAE1D,IAAI;MACA,MAAMoB,QAAQ,GAAG,MAAM/B,SAAS,CAAC0B,MAAM,CAAC,CAACE,GAAG,EAAEjB,QAAQ,CAAC;MACvDkB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACC,IAAI,CAAC;MACtDxB,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOyB,KAAU,EAAE;MAAA,IAAAC,eAAA;MACjBL,OAAO,CAACI,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DJ,OAAO,CAACI,KAAK,CAAC,gBAAgB,GAAAC,eAAA,GAAED,KAAK,CAACF,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,CAAC;IACzD;EACJ,CAAC;EAED,oBACI3B,OAAA,CAACF,IAAI;IAACgC,QAAQ,EAAEZ,YAAa;IAAAa,QAAA,gBACzB/B,OAAA,CAACF,IAAI,CAACkC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB/B,OAAA,CAACF,IAAI,CAACoC,KAAK;QAAAH,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACvCtC,OAAA,CAACF,IAAI,CAACyC,OAAO;QACTC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEnC,QAAQ,CAACE,IAAK;QACrBkC,QAAQ,EAAGvB,CAAC,IAAKZ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEE,IAAI,EAAEW,CAAC,CAACwB,MAAM,CAACF;QAAM,CAAC,CAAE;QACpEG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEbtC,OAAA,CAACF,IAAI,CAACkC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB/B,OAAA,CAACF,IAAI,CAACoC,KAAK;QAAAH,QAAA,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC1CtC,OAAA,CAACF,IAAI,CAACyC,OAAO;QACTC,IAAI,EAAC,QAAQ;QACbK,IAAI,EAAC,MAAM;QACXJ,KAAK,EAAEnC,QAAQ,CAACG,eAAgB;QAChCiC,QAAQ,EAAGvB,CAAC,IAAKZ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEG,eAAe,EAAEqC,UAAU,CAAC3B,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QAChGG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEbtC,OAAA,CAACF,IAAI,CAACkC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB/B,OAAA,CAACF,IAAI,CAACoC,KAAK;QAAAH,QAAA,EAAC;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxCtC,OAAA,CAACF,IAAI,CAACyC,OAAO;QACTC,IAAI,EAAC,QAAQ;QACbC,KAAK,EAAEnC,QAAQ,CAACI,gBAAiB;QACjCgC,QAAQ,EAAGvB,CAAC,IAAKZ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEI,gBAAgB,EAAEqC,QAAQ,CAAC5B,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QAC/FG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEbtC,OAAA,CAACF,IAAI,CAACkC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB/B,OAAA,CAACF,IAAI,CAACoC,KAAK;QAAAH,QAAA,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC1CtC,OAAA,CAACF,IAAI,CAACyC,OAAO;QACTC,IAAI,EAAC,QAAQ;QACbK,IAAI,EAAC,MAAM;QACXJ,KAAK,EAAEnC,QAAQ,CAACK,WAAY;QAC5B+B,QAAQ,EAAGvB,CAAC,IAAKZ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEK,WAAW,EAAEmC,UAAU,CAAC3B,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QAC5FG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACFtC,OAAA,CAACF,IAAI,CAACkD,IAAI;QAACf,SAAS,EAAC,YAAY;QAAAF,QAAA,GAAC,+BACJ,EAAC,CAACzB,QAAQ,CAACG,eAAe,GAAGH,QAAQ,CAACI,gBAAgB,EAAEuC,OAAO,CAAC,CAAC,CAAC,EAAC,SACjG;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEbtC,OAAA,CAACF,IAAI,CAACkC,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB/B,OAAA,CAACF,IAAI,CAACoC,KAAK;QAAAH,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5CtC,OAAA,CAACF,IAAI,CAACyC,OAAO;QACTC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEnC,QAAQ,CAACM,WAAY;QAC5B8B,QAAQ,EAAGvB,CAAC,IAAKZ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEM,WAAW,EAAEO,CAAC,CAACwB,MAAM,CAACF;QAAM,CAAC,CAAE;QAC3EG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEbtC,OAAA;MAAKiC,SAAS,EAAC,kCAAkC;MAAAF,QAAA,gBAC7C/B,OAAA,CAACH,MAAM;QAACqD,OAAO,EAAC,WAAW;QAACC,OAAO,EAAE/C,QAAS;QAAA2B,QAAA,EAAC;MAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/DtC,OAAA,CAACH,MAAM;QAACqD,OAAO,EAAC,SAAS;QAACV,IAAI,EAAC,QAAQ;QAAAT,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEf,CAAC;AAACjC,EAAA,CAjGIJ,QAA4F;AAAAmD,EAAA,GAA5FnD,QAA4F;AAmGlG,MAAMoD,IAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,KAAK,EAAE+B,QAAQ,CAAC,GAAGjE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAuB,IAAI,CAAC;EAC5E,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMwE,UAAU,GAAGA,CAAA,KAAM;IACvBR,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEdhE,SAAS,CAACwE,GAAG,CAAC,OAAO,CAAC,CACnBC,IAAI,CAAC1C,QAAQ,IAAI;MAChB8B,QAAQ,CAAC9B,QAAQ,CAACC,IAAI,CAAC;MACvB+B,UAAU,CAAC,KAAK,CAAC;MACjBO,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDI,KAAK,CAACzC,KAAK,IAAI;MACdJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MAEvC,IAAIoC,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAAChD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BqD,UAAU,CAAC,MAAMJ,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC;MACtC,CAAC,MAAM;QACLP,QAAQ,CAAC,0CAA0C,CAAC;QACpDD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDjE,SAAS,CAAC,MAAM;IACdyE,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvBV,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,IAAI,CAAC;IACrBG,UAAU,CAAC,CAAC;EACd,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOlD,EAAU,IAAK;IACzC,IAAImD,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACnE,IAAI;QACA,MAAM/E,SAAS,CAACgF,MAAM,CAAC,SAASrD,EAAE,EAAE,CAAC;QACrC4C,UAAU,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOtC,KAAK,EAAE;QACZJ,OAAO,CAACI,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE;IACJ;EACF,CAAC;EAED,IAAI6B,OAAO,IAAI,CAACG,SAAS,EAAE,oBAAO5D,OAAA;IAAA+B,QAAA,EAAG;EAAa;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACtD,IAAIV,KAAK,EAAE,oBACT5B,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAGiC,SAAS,EAAC,aAAa;MAAAF,QAAA,EAAEH;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtCtC,OAAA;MAAQiC,SAAS,EAAC,iBAAiB;MAACkB,OAAO,EAAEe,UAAW;MAAAnC,QAAA,EAAC;IAEzD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAGR,MAAMsC,SAAS,GAAGrB,KAAK,CAACsB,MAAM,CAAC,CAACC,GAAG,EAAE5E,IAAI,KAAK4E,GAAG,GAAG5E,IAAI,CAACS,WAAW,EAAE,CAAC,CAAC;EAExE,oBACEX,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAKiC,SAAS,EAAC,wDAAwD;MAAAF,QAAA,gBACrE/B,OAAA;QAAA+B,QAAA,EAAI;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBtC,OAAA,CAACH,MAAM;QAACqD,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAM;UAAEY,eAAe,CAAC,CAAC,CAAC,CAAC;UAAEF,YAAY,CAAC,IAAI,CAAC;QAAE,CAAE;QAAA9B,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eAENtC,OAAA;MAAOiC,SAAS,EAAC,iCAAiC;MAAAF,QAAA,gBAChD/B,OAAA;QAAOiC,SAAS,EAAC,YAAY;QAAAF,QAAA,eAC3B/B,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAA+B,QAAA,EAAI;UAAG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZtC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CtC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CtC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CtC,OAAA;YAAIiC,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDtC,OAAA;YAAIiC,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRtC,OAAA;QAAA+B,QAAA,EACGwB,KAAK,CAACwB,GAAG,CAAC7E,IAAI,iBACbF,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAA+B,QAAA,EAAK7B,IAAI,CAACM;UAAI;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpBtC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAIiD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAAClF,IAAI,CAACO,eAAe;UAAC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnItC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE7B,IAAI,CAACQ;UAAgB;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrDtC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAIiD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAAClF,IAAI,CAACS,WAAW;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/HtC,OAAA;YAAIiC,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAE,IAAIlB,IAAI,CAACX,IAAI,CAACU,WAAW,CAAC,CAACyE,kBAAkB,CAAC,OAAO;UAAC;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzFtC,OAAA;YAAIiC,SAAS,EAAC,aAAa;YAAAF,QAAA,gBACzB/B,OAAA,CAACH,MAAM;cACLqD,OAAO,EAAC,iBAAiB;cACzBoC,IAAI,EAAC,IAAI;cACTrD,SAAS,EAAC,MAAM;cAChBkB,OAAO,EAAEA,CAAA,KAAM;gBAAEY,eAAe,CAAC7D,IAAI,CAAC;gBAAE2D,YAAY,CAAC,IAAI,CAAC;cAAE,CAAE;cAAA9B,QAAA,EAC/D;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA,CAACH,MAAM;cACLqD,OAAO,EAAC,gBAAgB;cACxBoC,IAAI,EAAC,IAAI;cACTnC,OAAO,EAAEA,CAAA,KAAMqB,YAAY,CAACtE,IAAI,CAACoB,EAAE,CAAE;cAAAS,QAAA,EACtC;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GAtBEpC,IAAI,CAACoB,EAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBZ,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRtC,OAAA;QAAA+B,QAAA,eACE/B,OAAA;UAAIiC,SAAS,EAAC,YAAY;UAAAF,QAAA,gBACxB/B,OAAA;YAAIuF,OAAO,EAAE,CAAE;YAAAxD,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BtC,OAAA;YAAIiC,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAIiD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAACR,SAAS;UAAC;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxHtC,OAAA;YAAIuF,OAAO,EAAE;UAAE;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAERtC,OAAA,CAACJ,KAAK;MAAC4F,IAAI,EAAE5B,SAAU;MAAC6B,MAAM,EAAEA,CAAA,KAAM5B,YAAY,CAAC,KAAK,CAAE;MAACyB,IAAI,EAAC,IAAI;MAAAvD,QAAA,gBAClE/B,OAAA,CAACJ,KAAK,CAAC8F,MAAM;QAACC,WAAW;QAAA5D,QAAA,eACvB/B,OAAA,CAACJ,KAAK,CAACgG,KAAK;UAAA7D,QAAA,GAAE+B,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAExC,EAAE,GAAG,UAAU,GAAG,SAAS,EAAC,WAAS;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACftC,OAAA,CAACJ,KAAK,CAACiG,IAAI;QAAA9D,QAAA,eACT/B,OAAA,CAACC,QAAQ;UACPC,IAAI,EAAE4D,YAAa;UACnB3D,MAAM,EAAEoE,UAAW;UACnBnE,QAAQ,EAAEA,CAAA,KAAMyD,YAAY,CAAC,KAAK;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACgB,GAAA,CArIID,IAAc;AAAAyC,GAAA,GAAdzC,IAAc;AAuIpB,eAAeA,IAAI;AAAC,IAAAD,EAAA,EAAA0C,GAAA;AAAAC,YAAA,CAAA3C,EAAA;AAAA2C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}