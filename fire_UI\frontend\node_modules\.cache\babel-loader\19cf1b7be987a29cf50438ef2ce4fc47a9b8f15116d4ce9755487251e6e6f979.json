{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\FireTarget.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FireTarget = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  useEffect(() => {\n    apiClient.get('/fire-target').then(response => {\n      setData(response.data);\n      setLoading(false);\n    }).catch(error => {\n      setError('Erreur lors de la récupération des données FIRE.');\n      setLoading(false);\n      console.error(error);\n    });\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"text-danger\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 21\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 21\n  }, this);\n  const formatCurrency = value => new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(value);\n  const progressPercentage = data.current_net_patrimoine / data.fire_target_amount * 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"mb-4\",\n      children: \"Objectif FIRE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: \"Progression vers votre Objectif\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Vous avez atteint \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formatCurrency(data.current_net_patrimoine)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 35\n              }, this), \" sur votre objectif de \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formatCurrency(data.fire_target_amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 120\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress\",\n              style: {\n                height: '25px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-bar progress-bar-striped progress-bar-animated\",\n                role: \"progressbar\",\n                style: {\n                  width: `${progressPercentage}%`\n                },\n                \"aria-valuenow\": progressPercentage,\n                \"aria-valuemin\": 0,\n                \"aria-valuemax\": 100,\n                children: [progressPercentage.toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Montant restant \\xE0 investir :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), \" \", formatCurrency(data.remaining_to_invest)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"Revenu passif annuel potentiel (SWR \", data.secure_withdrawal_rate * 100, \"%) :\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), \" \", formatCurrency(data.potential_passive_income)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(FireTarget, \"OfDpPZA5krn7xMvjWiPniKd42GY=\");\n_c = FireTarget;\nexport default FireTarget;\nvar _c;\n$RefreshReg$(_c, \"FireTarget\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "jsxDEV", "_jsxDEV", "FireTarget", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "retryCount", "setRetryCount", "get", "then", "response", "catch", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "progressPercentage", "current_net_patrimoine", "fire_target_amount", "height", "role", "width", "toFixed", "remaining_to_invest", "secure_withdrawal_rate", "potential_passive_income", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/FireTarget.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\n\ninterface FireTargetData {\n  fire_target_amount: number;\n  secure_withdrawal_rate: number;\n  current_net_patrimoine: number;\n  remaining_to_invest: number;\n  potential_passive_income: number;\n}\n\nconst FireTarget: React.FC = () => {\n  const [data, setData] = useState<FireTargetData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  useEffect(() => {\n    apiClient.get('/fire-target')\n      .then(response => {\n        setData(response.data);\n        setLoading(false);\n      })\n      .catch(error => {\n        setError('Erreur lors de la récupération des données FIRE.');\n        setLoading(false);\n        console.error(error);\n      });\n  }, []);\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return <p className=\"text-danger\">{error}</p>;\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\n  const progressPercentage = (data.current_net_patrimoine / data.fire_target_amount) * 100;\n\n  return (\n    <div>\n      <h1 className=\"mb-4\">Objectif FIRE</h1>\n      <div className=\"row\">\n        <div className=\"col-md-8\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">Progression vers votre Objectif</h5>\n              <p>\n                Vous avez atteint <strong>{formatCurrency(data.current_net_patrimoine)}</strong> sur votre objectif de <strong>{formatCurrency(data.fire_target_amount)}</strong>.\n              </p>\n              <div className=\"progress\" style={{ height: '25px' }}>\n                <div\n                  className=\"progress-bar progress-bar-striped progress-bar-animated\"\n                  role=\"progressbar\"\n                  style={{ width: `${progressPercentage}%` }}\n                  aria-valuenow={progressPercentage}\n                  aria-valuemin={0}\n                  aria-valuemax={100}\n                >\n                  {progressPercentage.toFixed(2)}%\n                </div>\n              </div>\n              <hr />\n              <p>\n                <strong>Montant restant à investir :</strong> {formatCurrency(data.remaining_to_invest)}\n              </p>\n              <p>\n                <strong>Revenu passif annuel potentiel (SWR {data.secure_withdrawal_rate * 100}%) :</strong> {formatCurrency(data.potential_passive_income)}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FireTarget;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUpC,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGP,QAAQ,CAAwB,IAAI,CAAC;EAC7D,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACdE,SAAS,CAACa,GAAG,CAAC,cAAc,CAAC,CAC1BC,IAAI,CAACC,QAAQ,IAAI;MAChBT,OAAO,CAACS,QAAQ,CAACV,IAAI,CAAC;MACtBG,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDQ,KAAK,CAACP,KAAK,IAAI;MACdC,QAAQ,CAAC,kDAAkD,CAAC;MAC5DF,UAAU,CAAC,KAAK,CAAC;MACjBS,OAAO,CAACR,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,OAAO,EAAE,oBAAOL,OAAA;IAAAgB,QAAA,EAAG;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIb,KAAK,EAAE,oBAAOP,OAAA;IAAGqB,SAAS,EAAC,aAAa;IAAAL,QAAA,EAAET;EAAK;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EACxD,IAAI,CAACjB,IAAI,EAAE,oBAAOH,OAAA;IAAAgB,QAAA,EAAG;EAAyB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAME,cAAc,GAAIC,KAAa,IAAK,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAAEC,KAAK,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAC9H,MAAMM,kBAAkB,GAAI1B,IAAI,CAAC2B,sBAAsB,GAAG3B,IAAI,CAAC4B,kBAAkB,GAAI,GAAG;EAExF,oBACE/B,OAAA;IAAAgB,QAAA,gBACEhB,OAAA;MAAIqB,SAAS,EAAC,MAAM;MAAAL,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvCpB,OAAA;MAAKqB,SAAS,EAAC,KAAK;MAAAL,QAAA,eAClBhB,OAAA;QAAKqB,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBhB,OAAA;UAAKqB,SAAS,EAAC,MAAM;UAAAL,QAAA,eACnBhB,OAAA;YAAKqB,SAAS,EAAC,WAAW;YAAAL,QAAA,gBACxBhB,OAAA;cAAIqB,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DpB,OAAA;cAAAgB,QAAA,GAAG,oBACiB,eAAAhB,OAAA;gBAAAgB,QAAA,EAASM,cAAc,CAACnB,IAAI,CAAC2B,sBAAsB;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,2BAAuB,eAAApB,OAAA;gBAAAgB,QAAA,EAASM,cAAc,CAACnB,IAAI,CAAC4B,kBAAkB;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACnK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpB,OAAA;cAAKqB,SAAS,EAAC,UAAU;cAACK,KAAK,EAAE;gBAAEM,MAAM,EAAE;cAAO,CAAE;cAAAhB,QAAA,eAClDhB,OAAA;gBACEqB,SAAS,EAAC,yDAAyD;gBACnEY,IAAI,EAAC,aAAa;gBAClBP,KAAK,EAAE;kBAAEQ,KAAK,EAAE,GAAGL,kBAAkB;gBAAI,CAAE;gBAC3C,iBAAeA,kBAAmB;gBAClC,iBAAe,CAAE;gBACjB,iBAAe,GAAI;gBAAAb,QAAA,GAElBa,kBAAkB,CAACM,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpB,OAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAAgB,QAAA,EAAQ;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACE,cAAc,CAACnB,IAAI,CAACiC,mBAAmB,CAAC;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACJpB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAAgB,QAAA,GAAQ,sCAAoC,EAACb,IAAI,CAACkC,sBAAsB,GAAG,GAAG,EAAC,MAAI;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACE,cAAc,CAACnB,IAAI,CAACmC,wBAAwB,CAAC;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA9DID,UAAoB;AAAAsC,EAAA,GAApBtC,UAAoB;AAgE1B,eAAeA,UAAU;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}