{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\FireTarget.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form, ProgressBar } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FireSettingsForm = ({\n  settings,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    fire_target_amount: (settings === null || settings === void 0 ? void 0 : settings.fire_target_amount) || 910150,\n    secure_withdrawal_rate: ((settings === null || settings === void 0 ? void 0 : settings.secure_withdrawal_rate) || 0.04) * 100 // Convert to percentage for display\n  });\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const dataToSend = {\n      fire_target_amount: formData.fire_target_amount,\n      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100 // Convert back to decimal\n    };\n    console.log('Updating FIRE settings:', dataToSend);\n    try {\n      const response = await apiClient.put('/fire-settings', dataToSend);\n      console.log('FIRE settings updated successfully:', response.data);\n      onSave();\n    } catch (error) {\n      var _error$response;\n      console.error(\"Erreur lors de la sauvegarde des paramètres FIRE\", error);\n      console.error(\"Error details:\", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Objectif FIRE (\\u20AC)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"1000\",\n        value: formData.fire_target_amount,\n        onChange: e => setFormData({\n          ...formData,\n          fire_target_amount: parseFloat(e.target.value) || 0\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n        className: \"text-muted\",\n        children: \"Montant total que vous souhaitez atteindre pour votre ind\\xE9pendance financi\\xE8re\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Taux de retrait s\\xE9curis\\xE9 (%)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"0.1\",\n        min: \"1\",\n        max: \"10\",\n        value: formData.secure_withdrawal_rate,\n        onChange: e => setFormData({\n          ...formData,\n          secure_withdrawal_rate: parseFloat(e.target.value) || 4\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n        className: \"text-muted\",\n        children: \"Pourcentage de votre patrimoine que vous pouvez retirer annuellement (r\\xE8gle des 4% = 4.0)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-end gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onCancel,\n        children: \"Annuler\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        type: \"submit\",\n        children: \"Sauvegarder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(FireSettingsForm, \"NWi8mFpjauSzY6iBewN5nVDd2pQ=\");\n_c = FireSettingsForm;\nconst FireTarget = () => {\n  _s2();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const [showModal, setShowModal] = useState(false);\n  const fetchFireTargetData = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/fire-target').then(response => {\n      setData(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('FireTarget API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchFireTargetData(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des données FIRE.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchFireTargetData();\n  }, []);\n  const handleSettingsSave = () => {\n    setShowModal(false);\n    fetchFireTargetData(); // Refresh data after settings update\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchFireTargetData,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 21\n  }, this);\n  const formatCurrency = value => new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(value);\n  const formatPercentage = value => new Intl.NumberFormat('fr-FR', {\n    style: 'percent',\n    minimumFractionDigits: 1\n  }).format(value / 100);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Objectif FIRE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowModal(true),\n        children: \"Modifier l'objectif\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: \"Progression vers votre Objectif FIRE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-primary\",\n                    children: formatCurrency(data.current_net_patrimoine)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Patrimoine Net Actuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-success\",\n                    children: formatCurrency(data.fire_target_amount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Objectif FIRE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-info\",\n                    children: formatPercentage(data.progress_percentage)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Progression\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n              now: data.progress_percentage,\n              label: `${data.progress_percentage.toFixed(1)}%`,\n              style: {\n                height: '30px'\n              },\n              className: \"mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Montant restant \\xE0 investir :\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-danger fs-5\",\n                  children: formatCurrency(Math.max(0, data.remaining_to_invest))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Revenu passif annuel potentiel :\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-success fs-5\",\n                  children: formatCurrency(data.potential_passive_income)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [\"Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la r\\xE8gle des \", formatPercentage(data.secure_withdrawal_rate * 100)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-success mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Actifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: formatCurrency(data.total_assets)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"card-text\",\n              children: \"Incluant les SCPI et tous vos investissements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-danger mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Passifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: formatCurrency(data.total_liabilities)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"card-text\",\n              children: \"Emprunts et dettes en cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-info mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Taux de Retrait S\\xE9curis\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: formatPercentage(data.secure_withdrawal_rate * 100)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"card-text\",\n              children: \"Pourcentage de retrait annuel s\\xE9curis\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Modifier les Param\\xE8tres FIRE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(FireSettingsForm, {\n          settings: data ? {\n            fire_target_amount: data.fire_target_amount,\n            secure_withdrawal_rate: data.secure_withdrawal_rate\n          } : null,\n          onSave: handleSettingsSave,\n          onCancel: () => setShowModal(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s2(FireTarget, \"guE780lgw85iT/qxPW4RqWbWqkY=\");\n_c2 = FireTarget;\nexport default FireTarget;\nvar _c, _c2;\n$RefreshReg$(_c, \"FireSettingsForm\");\n$RefreshReg$(_c2, \"FireTarget\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Modal", "<PERSON><PERSON>", "Form", "ProgressBar", "jsxDEV", "_jsxDEV", "FireSettingsForm", "settings", "onSave", "onCancel", "_s", "formData", "setFormData", "fire_target_amount", "secure_withdrawal_rate", "handleSubmit", "e", "preventDefault", "dataToSend", "console", "log", "response", "put", "data", "error", "_error$response", "onSubmit", "children", "Group", "className", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "type", "step", "value", "onChange", "parseFloat", "target", "required", "Text", "min", "max", "variant", "onClick", "_c", "FireTarget", "_s2", "setData", "loading", "setLoading", "setError", "retryCount", "setRetryCount", "showModal", "setShowModal", "fetchFireTargetData", "get", "then", "catch", "prev", "setTimeout", "handleSettingsSave", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "formatPercentage", "minimumFractionDigits", "current_net_patrimoine", "progress_percentage", "now", "label", "toFixed", "height", "Math", "remaining_to_invest", "potential_passive_income", "total_assets", "total_liabilities", "show", "onHide", "size", "Header", "closeButton", "Title", "Body", "_c2", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/FireTarget.tsx"], "sourcesContent": ["import React, { useEffect, useState, FormEvent } from 'react';\nimport apiClient from './ApiClient';\nimport { <PERSON><PERSON>, Button, Form, ProgressBar } from 'react-bootstrap';\n\ninterface FireTargetData {\n  fire_target_amount: number;\n  secure_withdrawal_rate: number;\n  current_net_patrimoine: number;\n  total_assets: number;\n  total_liabilities: number;\n  remaining_to_invest: number;\n  potential_passive_income: number;\n  progress_percentage: number;\n}\n\ninterface FireSettings {\n  id: number;\n  fire_target_amount: number;\n  secure_withdrawal_rate: number;\n  update_date: string;\n}\n\nconst FireSettingsForm: React.FC<{\n  settings: { fire_target_amount: number; secure_withdrawal_rate: number } | null,\n  onSave: () => void,\n  onCancel: () => void\n}> = ({ settings, onSave, onCancel }) => {\n  const [formData, setFormData] = useState({\n    fire_target_amount: settings?.fire_target_amount || 910150,\n    secure_withdrawal_rate: (settings?.secure_withdrawal_rate || 0.04) * 100, // Convert to percentage for display\n  });\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n\n    const dataToSend = {\n      fire_target_amount: formData.fire_target_amount,\n      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100, // Convert back to decimal\n    };\n\n    console.log('Updating FIRE settings:', dataToSend);\n\n    try {\n      const response = await apiClient.put('/fire-settings', dataToSend);\n      console.log('FIRE settings updated successfully:', response.data);\n      onSave();\n    } catch (error: any) {\n      console.error(\"Erreur lors de la sauvegarde des paramètres FIRE\", error);\n      console.error(\"Error details:\", error.response?.data);\n    }\n  };\n\n  return (\n    <Form onSubmit={handleSubmit}>\n      <Form.Group className=\"mb-3\">\n        <Form.Label>Objectif FIRE (€)</Form.Label>\n        <Form.Control\n          type=\"number\"\n          step=\"1000\"\n          value={formData.fire_target_amount}\n          onChange={(e) => setFormData({ ...formData, fire_target_amount: parseFloat(e.target.value) || 0 })}\n          required\n        />\n        <Form.Text className=\"text-muted\">\n          Montant total que vous souhaitez atteindre pour votre indépendance financière\n        </Form.Text>\n      </Form.Group>\n\n      <Form.Group className=\"mb-3\">\n        <Form.Label>Taux de retrait sécurisé (%)</Form.Label>\n        <Form.Control\n          type=\"number\"\n          step=\"0.1\"\n          min=\"1\"\n          max=\"10\"\n          value={formData.secure_withdrawal_rate}\n          onChange={(e) => setFormData({ ...formData, secure_withdrawal_rate: parseFloat(e.target.value) || 4 })}\n          required\n        />\n        <Form.Text className=\"text-muted\">\n          Pourcentage de votre patrimoine que vous pouvez retirer annuellement (règle des 4% = 4.0)\n        </Form.Text>\n      </Form.Group>\n\n      <div className=\"d-flex justify-content-end gap-2\">\n        <Button variant=\"secondary\" onClick={onCancel}>Annuler</Button>\n        <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\n      </div>\n    </Form>\n  );\n};\n\nconst FireTarget: React.FC = () => {\n  const [data, setData] = useState<FireTargetData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const [showModal, setShowModal] = useState(false);\n\n  const fetchFireTargetData = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/fire-target')\n      .then(response => {\n        setData(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('FireTarget API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchFireTargetData(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des données FIRE.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchFireTargetData();\n  }, []);\n\n  const handleSettingsSave = () => {\n    setShowModal(false);\n    fetchFireTargetData(); // Refresh data after settings update\n  };\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchFireTargetData}>\n        Réessayer\n      </button>\n    </div>\n  );\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\n  const formatPercentage = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 1 }).format(value / 100);\n\n  return (\n    <div>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1>Objectif FIRE</h1>\n        <Button\n          variant=\"primary\"\n          onClick={() => setShowModal(true)}\n        >\n          Modifier l'objectif\n        </Button>\n      </div>\n\n      {/* Progression Card */}\n      <div className=\"row mb-4\">\n        <div className=\"col-12\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">Progression vers votre Objectif FIRE</h5>\n              <div className=\"row mb-3\">\n                <div className=\"col-md-4\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-primary\">{formatCurrency(data.current_net_patrimoine)}</h3>\n                    <small className=\"text-muted\">Patrimoine Net Actuel</small>\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-success\">{formatCurrency(data.fire_target_amount)}</h3>\n                    <small className=\"text-muted\">Objectif FIRE</small>\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-info\">{formatPercentage(data.progress_percentage)}</h3>\n                    <small className=\"text-muted\">Progression</small>\n                  </div>\n                </div>\n              </div>\n\n              <ProgressBar\n                now={data.progress_percentage}\n                label={`${data.progress_percentage.toFixed(1)}%`}\n                style={{ height: '30px' }}\n                className=\"mb-3\"\n              />\n\n              <div className=\"row\">\n                <div className=\"col-md-6\">\n                  <p className=\"mb-1\">\n                    <strong>Montant restant à investir :</strong>\n                  </p>\n                  <p className=\"text-danger fs-5\">\n                    {formatCurrency(Math.max(0, data.remaining_to_invest))}\n                  </p>\n                </div>\n                <div className=\"col-md-6\">\n                  <p className=\"mb-1\">\n                    <strong>Revenu passif annuel potentiel :</strong>\n                  </p>\n                  <p className=\"text-success fs-5\">\n                    {formatCurrency(data.potential_passive_income)}\n                  </p>\n                  <small className=\"text-muted\">\n                    Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la règle des {formatPercentage(data.secure_withdrawal_rate * 100)}\n                  </small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Details Cards */}\n      <div className=\"row\">\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-success mb-3\">\n            <div className=\"card-header\">Total Actifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{formatCurrency(data.total_assets)}</h5>\n              <p className=\"card-text\">Incluant les SCPI et tous vos investissements</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-danger mb-3\">\n            <div className=\"card-header\">Total Passifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{formatCurrency(data.total_liabilities)}</h5>\n              <p className=\"card-text\">Emprunts et dettes en cours</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-info mb-3\">\n            <div className=\"card-header\">Taux de Retrait Sécurisé</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{formatPercentage(data.secure_withdrawal_rate * 100)}</h5>\n              <p className=\"card-text\">Pourcentage de retrait annuel sécurisé</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Settings Modal */}\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Modifier les Paramètres FIRE</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <FireSettingsForm\n            settings={data ? {\n              fire_target_amount: data.fire_target_amount,\n              secure_withdrawal_rate: data.secure_withdrawal_rate\n            } : null}\n            onSave={handleSettingsSave}\n            onCancel={() => setShowModal(false)}\n          />\n        </Modal.Body>\n      </Modal>\n    </div>\n  );\n};\n\nexport default FireTarget;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAC7D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBnE,MAAMC,gBAIJ,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,kBAAkB,EAAE,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,kBAAkB,KAAI,MAAM;IAC1DC,sBAAsB,EAAE,CAAC,CAAAP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,sBAAsB,KAAI,IAAI,IAAI,GAAG,CAAE;EAC5E,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,MAAOC,CAAY,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,UAAU,GAAG;MACjBL,kBAAkB,EAAEF,QAAQ,CAACE,kBAAkB;MAC/CC,sBAAsB,EAAEH,QAAQ,CAACG,sBAAsB,GAAG,GAAG,CAAE;IACjE,CAAC;IAEDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,UAAU,CAAC;IAElD,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMtB,SAAS,CAACuB,GAAG,CAAC,gBAAgB,EAAEJ,UAAU,CAAC;MAClEC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACjEf,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOgB,KAAU,EAAE;MAAA,IAAAC,eAAA;MACnBN,OAAO,CAACK,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxEL,OAAO,CAACK,KAAK,CAAC,gBAAgB,GAAAC,eAAA,GAAED,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,CAAC;IACvD;EACF,CAAC;EAED,oBACElB,OAAA,CAACH,IAAI;IAACwB,QAAQ,EAAEX,YAAa;IAAAY,QAAA,gBAC3BtB,OAAA,CAACH,IAAI,CAAC0B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBAC1BtB,OAAA,CAACH,IAAI,CAAC4B,KAAK;QAAAH,QAAA,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC1C7B,OAAA,CAACH,IAAI,CAACiC,OAAO;QACXC,IAAI,EAAC,QAAQ;QACbC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAE3B,QAAQ,CAACE,kBAAmB;QACnC0B,QAAQ,EAAGvB,CAAC,IAAKJ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEE,kBAAkB,EAAE2B,UAAU,CAACxB,CAAC,CAACyB,MAAM,CAACH,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QACnGI,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF7B,OAAA,CAACH,IAAI,CAACyC,IAAI;QAACd,SAAS,EAAC,YAAY;QAAAF,QAAA,EAAC;MAElC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEb7B,OAAA,CAACH,IAAI,CAAC0B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBAC1BtB,OAAA,CAACH,IAAI,CAAC4B,KAAK;QAAAH,QAAA,EAAC;MAA4B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACrD7B,OAAA,CAACH,IAAI,CAACiC,OAAO;QACXC,IAAI,EAAC,QAAQ;QACbC,IAAI,EAAC,KAAK;QACVO,GAAG,EAAC,GAAG;QACPC,GAAG,EAAC,IAAI;QACRP,KAAK,EAAE3B,QAAQ,CAACG,sBAAuB;QACvCyB,QAAQ,EAAGvB,CAAC,IAAKJ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEG,sBAAsB,EAAE0B,UAAU,CAACxB,CAAC,CAACyB,MAAM,CAACH,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QACvGI,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF7B,OAAA,CAACH,IAAI,CAACyC,IAAI;QAACd,SAAS,EAAC,YAAY;QAAAF,QAAA,EAAC;MAElC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEb7B,OAAA;MAAKwB,SAAS,EAAC,kCAAkC;MAAAF,QAAA,gBAC/CtB,OAAA,CAACJ,MAAM;QAAC6C,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEtC,QAAS;QAAAkB,QAAA,EAAC;MAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/D7B,OAAA,CAACJ,MAAM;QAAC6C,OAAO,EAAC,SAAS;QAACV,IAAI,EAAC,QAAQ;QAAAT,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACxB,EAAA,CApEIJ,gBAIJ;AAAA0C,EAAA,GAJI1C,gBAIJ;AAkEF,MAAM2C,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM,CAAC3B,IAAI,EAAE4B,OAAO,CAAC,GAAGrD,QAAQ,CAAwB,IAAI,CAAC;EAC7D,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAE8B,QAAQ,CAAC,GAAGxD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM6D,mBAAmB,GAAGA,CAAA,KAAM;IAChCN,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEdvD,SAAS,CAAC6D,GAAG,CAAC,cAAc,CAAC,CAC1BC,IAAI,CAACxC,QAAQ,IAAI;MAChB8B,OAAO,CAAC9B,QAAQ,CAACE,IAAI,CAAC;MACtB8B,UAAU,CAAC,KAAK,CAAC;MACjBG,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDM,KAAK,CAACtC,KAAK,IAAI;MACdL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAE7C,IAAI+B,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAML,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAC,MAAM;QACLL,QAAQ,CAAC,kDAAkD,CAAC;QAC5DD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDxD,SAAS,CAAC,MAAM;IACd8D,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/BP,YAAY,CAAC,KAAK,CAAC;IACnBC,mBAAmB,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,IAAIP,OAAO,EAAE,oBAAO/C,OAAA;IAAAsB,QAAA,EAAG;EAAa;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIV,KAAK,EAAE,oBACTnB,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAGwB,SAAS,EAAC,aAAa;MAAAF,QAAA,EAAEH;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtC7B,OAAA;MAAQwB,SAAS,EAAC,iBAAiB;MAACkB,OAAO,EAAEY,mBAAoB;MAAAhC,QAAA,EAAC;IAElE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAER,IAAI,CAACX,IAAI,EAAE,oBAAOlB,OAAA;IAAAsB,QAAA,EAAG;EAAyB;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAMgC,cAAc,GAAI5B,KAAa,IAAK,IAAI6B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAAEC,KAAK,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC;EAC9H,MAAMkC,gBAAgB,GAAIlC,KAAa,IAAK,IAAI6B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAAEC,KAAK,EAAE,SAAS;IAAEI,qBAAqB,EAAE;EAAE,CAAC,CAAC,CAACF,MAAM,CAACjC,KAAK,GAAG,GAAG,CAAC;EAE9I,oBACEjC,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAKwB,SAAS,EAAC,wDAAwD;MAAAF,QAAA,gBACrEtB,OAAA;QAAAsB,QAAA,EAAI;MAAa;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB7B,OAAA,CAACJ,MAAM;QACL6C,OAAO,EAAC,SAAS;QACjBC,OAAO,EAAEA,CAAA,KAAMW,YAAY,CAAC,IAAI,CAAE;QAAA/B,QAAA,EACnC;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,UAAU;MAAAF,QAAA,eACvBtB,OAAA;QAAKwB,SAAS,EAAC,QAAQ;QAAAF,QAAA,eACrBtB,OAAA;UAAKwB,SAAS,EAAC,MAAM;UAAAF,QAAA,eACnBtB,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBtB,OAAA;cAAIwB,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAC;YAAoC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpE7B,OAAA;cAAKwB,SAAS,EAAC,UAAU;cAAAF,QAAA,gBACvBtB,OAAA;gBAAKwB,SAAS,EAAC,UAAU;gBAAAF,QAAA,eACvBtB,OAAA;kBAAKwB,SAAS,EAAC,aAAa;kBAAAF,QAAA,gBAC1BtB,OAAA;oBAAIwB,SAAS,EAAC,cAAc;oBAAAF,QAAA,EAAEuC,cAAc,CAAC3C,IAAI,CAACmD,sBAAsB;kBAAC;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/E7B,OAAA;oBAAOwB,SAAS,EAAC,YAAY;oBAAAF,QAAA,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7B,OAAA;gBAAKwB,SAAS,EAAC,UAAU;gBAAAF,QAAA,eACvBtB,OAAA;kBAAKwB,SAAS,EAAC,aAAa;kBAAAF,QAAA,gBAC1BtB,OAAA;oBAAIwB,SAAS,EAAC,cAAc;oBAAAF,QAAA,EAAEuC,cAAc,CAAC3C,IAAI,CAACV,kBAAkB;kBAAC;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3E7B,OAAA;oBAAOwB,SAAS,EAAC,YAAY;oBAAAF,QAAA,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7B,OAAA;gBAAKwB,SAAS,EAAC,UAAU;gBAAAF,QAAA,eACvBtB,OAAA;kBAAKwB,SAAS,EAAC,aAAa;kBAAAF,QAAA,gBAC1BtB,OAAA;oBAAIwB,SAAS,EAAC,WAAW;oBAAAF,QAAA,EAAE6C,gBAAgB,CAACjD,IAAI,CAACoD,mBAAmB;kBAAC;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3E7B,OAAA;oBAAOwB,SAAS,EAAC,YAAY;oBAAAF,QAAA,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7B,OAAA,CAACF,WAAW;cACVyE,GAAG,EAAErD,IAAI,CAACoD,mBAAoB;cAC9BE,KAAK,EAAE,GAAGtD,IAAI,CAACoD,mBAAmB,CAACG,OAAO,CAAC,CAAC,CAAC,GAAI;cACjDT,KAAK,EAAE;gBAAEU,MAAM,EAAE;cAAO,CAAE;cAC1BlD,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eAEF7B,OAAA;cAAKwB,SAAS,EAAC,KAAK;cAAAF,QAAA,gBAClBtB,OAAA;gBAAKwB,SAAS,EAAC,UAAU;gBAAAF,QAAA,gBACvBtB,OAAA;kBAAGwB,SAAS,EAAC,MAAM;kBAAAF,QAAA,eACjBtB,OAAA;oBAAAsB,QAAA,EAAQ;kBAA4B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACJ7B,OAAA;kBAAGwB,SAAS,EAAC,kBAAkB;kBAAAF,QAAA,EAC5BuC,cAAc,CAACc,IAAI,CAACnC,GAAG,CAAC,CAAC,EAAEtB,IAAI,CAAC0D,mBAAmB,CAAC;gBAAC;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN7B,OAAA;gBAAKwB,SAAS,EAAC,UAAU;gBAAAF,QAAA,gBACvBtB,OAAA;kBAAGwB,SAAS,EAAC,MAAM;kBAAAF,QAAA,eACjBtB,OAAA;oBAAAsB,QAAA,EAAQ;kBAAgC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACJ7B,OAAA;kBAAGwB,SAAS,EAAC,mBAAmB;kBAAAF,QAAA,EAC7BuC,cAAc,CAAC3C,IAAI,CAAC2D,wBAAwB;gBAAC;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACJ7B,OAAA;kBAAOwB,SAAS,EAAC,YAAY;kBAAAF,QAAA,GAAC,kGACiE,EAAC6C,gBAAgB,CAACjD,IAAI,CAACT,sBAAsB,GAAG,GAAG,CAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,KAAK;MAAAF,QAAA,gBAClBtB,OAAA;QAAKwB,SAAS,EAAC,UAAU;QAAAF,QAAA,eACvBtB,OAAA;UAAKwB,SAAS,EAAC,iCAAiC;UAAAF,QAAA,gBAC9CtB,OAAA;YAAKwB,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/C7B,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBtB,OAAA;cAAIwB,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAEuC,cAAc,CAAC3C,IAAI,CAAC4D,YAAY;YAAC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnE7B,OAAA;cAAGwB,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAA6C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7B,OAAA;QAAKwB,SAAS,EAAC,UAAU;QAAAF,QAAA,eACvBtB,OAAA;UAAKwB,SAAS,EAAC,gCAAgC;UAAAF,QAAA,gBAC7CtB,OAAA;YAAKwB,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD7B,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBtB,OAAA;cAAIwB,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAEuC,cAAc,CAAC3C,IAAI,CAAC6D,iBAAiB;YAAC;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxE7B,OAAA;cAAGwB,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7B,OAAA;QAAKwB,SAAS,EAAC,UAAU;QAAAF,QAAA,eACvBtB,OAAA;UAAKwB,SAAS,EAAC,8BAA8B;UAAAF,QAAA,gBAC3CtB,OAAA;YAAKwB,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3D7B,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBtB,OAAA;cAAIwB,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAE6C,gBAAgB,CAACjD,IAAI,CAACT,sBAAsB,GAAG,GAAG;YAAC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrF7B,OAAA;cAAGwB,SAAS,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAsC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA,CAACL,KAAK;MAACqF,IAAI,EAAE5B,SAAU;MAAC6B,MAAM,EAAEA,CAAA,KAAM5B,YAAY,CAAC,KAAK,CAAE;MAAC6B,IAAI,EAAC,IAAI;MAAA5D,QAAA,gBAClEtB,OAAA,CAACL,KAAK,CAACwF,MAAM;QAACC,WAAW;QAAA9D,QAAA,eACvBtB,OAAA,CAACL,KAAK,CAAC0F,KAAK;UAAA/D,QAAA,EAAC;QAA4B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACf7B,OAAA,CAACL,KAAK,CAAC2F,IAAI;QAAAhE,QAAA,eACTtB,OAAA,CAACC,gBAAgB;UACfC,QAAQ,EAAEgB,IAAI,GAAG;YACfV,kBAAkB,EAAEU,IAAI,CAACV,kBAAkB;YAC3CC,sBAAsB,EAAES,IAAI,CAACT;UAC/B,CAAC,GAAG,IAAK;UACTN,MAAM,EAAEyD,kBAAmB;UAC3BxD,QAAQ,EAAEA,CAAA,KAAMiD,YAAY,CAAC,KAAK;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACgB,GAAA,CA9KID,UAAoB;AAAA2C,GAAA,GAApB3C,UAAoB;AAgL1B,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAA4C,GAAA;AAAAC,YAAA,CAAA7C,EAAA;AAAA6C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}