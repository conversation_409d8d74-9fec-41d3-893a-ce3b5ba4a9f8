[{"D:\\GIT\\fire_UI\\frontend\\src\\index.tsx": "1", "D:\\GIT\\fire_UI\\frontend\\src\\reportWebVitals.ts": "2", "D:\\GIT\\fire_UI\\frontend\\src\\App.tsx": "3", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Dashboard.tsx": "4", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Assets.tsx": "5", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Liabilities.tsx": "6", "D:\\GIT\\fire_UI\\frontend\\src\\components\\FireTarget.tsx": "7", "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiClient.ts": "8", "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiTest.tsx": "9"}, {"size": 601, "mtime": 1751356974925, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1751356950429, "results": "12", "hashOfConfig": "11"}, {"size": 2085, "mtime": 1751378097376, "results": "13", "hashOfConfig": "11"}, {"size": 3865, "mtime": 1751378458310, "results": "14", "hashOfConfig": "11"}, {"size": 9278, "mtime": 1751379114325, "results": "15", "hashOfConfig": "11"}, {"size": 7078, "mtime": 1751378644844, "results": "16", "hashOfConfig": "11"}, {"size": 3199, "mtime": 1751378685321, "results": "17", "hashOfConfig": "11"}, {"size": 487, "mtime": 1751378471081, "results": "18", "hashOfConfig": "11"}, {"size": 2626, "mtime": 1751378407120, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\GIT\\fire_UI\\frontend\\src\\index.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\App.tsx", ["47", "48", "49", "50", "51", "52"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Dashboard.tsx", ["53"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Assets.tsx", ["54"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Liabilities.tsx", ["55"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\FireTarget.tsx", ["56"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiClient.ts", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiTest.tsx", [], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 33, "column": 11, "nodeType": "59", "endLine": 33, "endColumn": 85}, {"ruleId": "57", "severity": 1, "message": "58", "line": 37, "column": 17, "nodeType": "59", "endLine": 37, "endColumn": 87}, {"ruleId": "57", "severity": 1, "message": "58", "line": 40, "column": 17, "nodeType": "59", "endLine": 40, "endColumn": 84}, {"ruleId": "57", "severity": 1, "message": "58", "line": 43, "column": 17, "nodeType": "59", "endLine": 43, "endColumn": 89}, {"ruleId": "57", "severity": 1, "message": "58", "line": 46, "column": 17, "nodeType": "59", "endLine": 46, "endColumn": 82}, {"ruleId": "57", "severity": 1, "message": "58", "line": 49, "column": 17, "nodeType": "59", "endLine": 49, "endColumn": 82}, {"ruleId": "60", "severity": 1, "message": "61", "line": 46, "column": 6, "nodeType": "62", "endLine": 46, "endColumn": 8, "suggestions": "63"}, {"ruleId": "60", "severity": 1, "message": "64", "line": 157, "column": 6, "nodeType": "62", "endLine": 157, "endColumn": 8, "suggestions": "65"}, {"ruleId": "60", "severity": 1, "message": "66", "line": 100, "column": 6, "nodeType": "62", "endLine": 100, "endColumn": 8, "suggestions": "67"}, {"ruleId": "60", "severity": 1, "message": "68", "line": 43, "column": 6, "nodeType": "62", "endLine": 43, "endColumn": 8, "suggestions": "69"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["70"], "React Hook useEffect has a missing dependency: 'fetchAssets'. Either include it or remove the dependency array.", ["71"], "React Hook useEffect has a missing dependency: 'fetchLiabilities'. Either include it or remove the dependency array.", ["72"], "React Hook useEffect has a missing dependency: 'fetchFireTargetData'. Either include it or remove the dependency array.", ["73"], {"desc": "74", "fix": "75"}, {"desc": "76", "fix": "77"}, {"desc": "78", "fix": "79"}, {"desc": "80", "fix": "81"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "82", "text": "83"}, "Update the dependencies array to be: [fetchAssets]", {"range": "84", "text": "85"}, "Update the dependencies array to be: [fetchLiabilities]", {"range": "86", "text": "87"}, "Update the dependencies array to be: [fetchFireTargetData]", {"range": "88", "text": "89"}, [1307, 1309], "[fetchDashboardData]", [6403, 6405], "[fetchAssets]", [4141, 4143], "[fetchLiabilities]", [1187, 1189], "[fetchFireTargetData]"]