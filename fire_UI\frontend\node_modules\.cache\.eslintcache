[{"D:\\GIT\\fire_UI\\frontend\\src\\index.tsx": "1", "D:\\GIT\\fire_UI\\frontend\\src\\reportWebVitals.ts": "2", "D:\\GIT\\fire_UI\\frontend\\src\\App.tsx": "3", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Dashboard.tsx": "4", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Assets.tsx": "5", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Liabilities.tsx": "6", "D:\\GIT\\fire_UI\\frontend\\src\\components\\FireTarget.tsx": "7", "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiClient.ts": "8", "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiTest.tsx": "9"}, {"size": 601, "mtime": 1751356974925, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1751356950429, "results": "12", "hashOfConfig": "11"}, {"size": 2085, "mtime": 1751378097376, "results": "13", "hashOfConfig": "11"}, {"size": 4344, "mtime": 1751378020176, "results": "14", "hashOfConfig": "11"}, {"size": 8181, "mtime": 1751364268146, "results": "15", "hashOfConfig": "11"}, {"size": 6657, "mtime": 1751358321556, "results": "16", "hashOfConfig": "11"}, {"size": 2680, "mtime": 1751358033098, "results": "17", "hashOfConfig": "11"}, {"size": 893, "mtime": 1751378043919, "results": "18", "hashOfConfig": "11"}, {"size": 1956, "mtime": 1751378062477, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\GIT\\fire_UI\\frontend\\src\\index.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\App.tsx", ["47", "48", "49", "50", "51", "52"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Dashboard.tsx", ["53"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Assets.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Liabilities.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\FireTarget.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiClient.ts", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiTest.tsx", [], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 33, "column": 11, "nodeType": "56", "endLine": 33, "endColumn": 85}, {"ruleId": "54", "severity": 1, "message": "55", "line": 37, "column": 17, "nodeType": "56", "endLine": 37, "endColumn": 87}, {"ruleId": "54", "severity": 1, "message": "55", "line": 40, "column": 17, "nodeType": "56", "endLine": 40, "endColumn": 84}, {"ruleId": "54", "severity": 1, "message": "55", "line": 43, "column": 17, "nodeType": "56", "endLine": 43, "endColumn": 89}, {"ruleId": "54", "severity": 1, "message": "55", "line": 46, "column": 17, "nodeType": "56", "endLine": 46, "endColumn": 82}, {"ruleId": "54", "severity": 1, "message": "55", "line": 49, "column": 17, "nodeType": "56", "endLine": 49, "endColumn": 82}, {"ruleId": "57", "severity": 1, "message": "58", "line": 56, "column": 6, "nodeType": "59", "endLine": 56, "endColumn": 8, "suggestions": "60"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["61"], {"desc": "62", "fix": "63"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "64", "text": "65"}, [1786, 1788], "[fetchDashboardData]"]