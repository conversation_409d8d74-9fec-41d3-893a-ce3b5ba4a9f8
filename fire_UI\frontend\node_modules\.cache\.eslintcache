[{"D:\\GIT\\fire_UI\\frontend\\src\\index.tsx": "1", "D:\\GIT\\fire_UI\\frontend\\src\\reportWebVitals.ts": "2", "D:\\GIT\\fire_UI\\frontend\\src\\App.tsx": "3", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Dashboard.tsx": "4", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Assets.tsx": "5", "D:\\GIT\\fire_UI\\frontend\\src\\components\\Liabilities.tsx": "6", "D:\\GIT\\fire_UI\\frontend\\src\\components\\FireTarget.tsx": "7", "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiClient.ts": "8", "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiTest.tsx": "9", "D:\\GIT\\fire_UI\\frontend\\src\\components\\SCPI.tsx": "10"}, {"size": 601, "mtime": 1751356974925, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1751356950429, "results": "13", "hashOfConfig": "12"}, {"size": 2326, "mtime": 1751379792434, "results": "14", "hashOfConfig": "12"}, {"size": 3865, "mtime": 1751378458310, "results": "15", "hashOfConfig": "12"}, {"size": 9351, "mtime": 1751379184754, "results": "16", "hashOfConfig": "12"}, {"size": 7078, "mtime": 1751378644844, "results": "17", "hashOfConfig": "12"}, {"size": 9538, "mtime": 1751380561698, "results": "18", "hashOfConfig": "12"}, {"size": 487, "mtime": 1751378471081, "results": "19", "hashOfConfig": "12"}, {"size": 2626, "mtime": 1751378407120, "results": "20", "hashOfConfig": "12"}, {"size": 8931, "mtime": 1751379754175, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\GIT\\fire_UI\\frontend\\src\\index.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\App.tsx", ["52", "53", "54", "55", "56", "57", "58"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Dashboard.tsx", ["59"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Assets.tsx", ["60"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\Liabilities.tsx", ["61"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\FireTarget.tsx", ["62", "63"], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiClient.ts", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\ApiTest.tsx", [], [], "D:\\GIT\\fire_UI\\frontend\\src\\components\\SCPI.tsx", ["64", "65", "66"], [], {"ruleId": "67", "severity": 1, "message": "68", "line": 36, "column": 11, "nodeType": "69", "endLine": 36, "endColumn": 85}, {"ruleId": "67", "severity": 1, "message": "68", "line": 40, "column": 17, "nodeType": "69", "endLine": 40, "endColumn": 87}, {"ruleId": "67", "severity": 1, "message": "68", "line": 43, "column": 17, "nodeType": "69", "endLine": 43, "endColumn": 84}, {"ruleId": "67", "severity": 1, "message": "68", "line": 46, "column": 17, "nodeType": "69", "endLine": 46, "endColumn": 89}, {"ruleId": "67", "severity": 1, "message": "68", "line": 49, "column": 17, "nodeType": "69", "endLine": 49, "endColumn": 82}, {"ruleId": "67", "severity": 1, "message": "68", "line": 52, "column": 17, "nodeType": "69", "endLine": 52, "endColumn": 82}, {"ruleId": "67", "severity": 1, "message": "68", "line": 55, "column": 17, "nodeType": "69", "endLine": 55, "endColumn": 82}, {"ruleId": "70", "severity": 1, "message": "71", "line": 46, "column": 6, "nodeType": "72", "endLine": 46, "endColumn": 8, "suggestions": "73"}, {"ruleId": "70", "severity": 1, "message": "74", "line": 157, "column": 6, "nodeType": "72", "endLine": 157, "endColumn": 8, "suggestions": "75"}, {"ruleId": "70", "severity": 1, "message": "76", "line": 100, "column": 6, "nodeType": "72", "endLine": 100, "endColumn": 8, "suggestions": "77"}, {"ruleId": "78", "severity": 1, "message": "79", "line": 16, "column": 11, "nodeType": "80", "messageId": "81", "endLine": 16, "endColumn": 23}, {"ruleId": "70", "severity": 1, "message": "82", "line": 125, "column": 6, "nodeType": "72", "endLine": 125, "endColumn": 8, "suggestions": "83"}, {"ruleId": "70", "severity": 1, "message": "84", "line": 29, "column": 8, "nodeType": "72", "endLine": 29, "endColumn": 61, "suggestions": "85"}, {"ruleId": "86", "severity": 1, "message": "87", "line": 113, "column": 7, "nodeType": "80", "messageId": "88", "endLine": 113, "endColumn": 21}, {"ruleId": "70", "severity": 1, "message": "89", "line": 146, "column": 6, "nodeType": "72", "endLine": 146, "endColumn": 8, "suggestions": "90"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["91"], "React Hook useEffect has a missing dependency: 'fetchAssets'. Either include it or remove the dependency array.", ["92"], "React Hook useEffect has a missing dependency: 'fetchLiabilities'. Either include it or remove the dependency array.", ["93"], "@typescript-eslint/no-unused-vars", "'FireSettings' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchFireTargetData'. Either include it or remove the dependency array.", ["94"], "React Hook useEffect has a missing dependency: 'formData.total_value'. Either include it or remove the dependency array.", ["95"], "@typescript-eslint/no-redeclare", "'SCPI' is already defined.", "redeclared", "React Hook useEffect has a missing dependency: 'fetchSCPIs'. Either include it or remove the dependency array.", ["96"], {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, {"desc": "101", "fix": "102"}, {"desc": "103", "fix": "104"}, {"desc": "105", "fix": "106"}, {"desc": "107", "fix": "108"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "109", "text": "110"}, "Update the dependencies array to be: [fetchAssets]", {"range": "111", "text": "112"}, "Update the dependencies array to be: [fetchLiabilities]", {"range": "113", "text": "114"}, "Update the dependencies array to be: [fetchFireTargetData]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [formData.price_per_share, formData.number_of_shares, formData.total_value]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [fetchSCPIs]", {"range": "119", "text": "120"}, [1307, 1309], "[fetchDashboardData]", [6403, 6405], "[fetchAssets]", [4141, 4143], "[fetchLiabilities]", [4010, 4012], "[fetchFireTargetData]", [1142, 1195], "[formData.price_per_share, formData.number_of_shares, formData.total_value]", [5429, 5431], "[fetchSCPIs]"]