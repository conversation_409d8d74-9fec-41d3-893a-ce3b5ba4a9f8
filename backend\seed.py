import datetime
from sqlalchemy.orm import Session
from database import SessionLocal, engine
import models, schemas

def seed_data():
    db = SessionLocal()
    models.Base.metadata.drop_all(bind=engine)
    models.Base.metadata.create_all(bind=engine)

    # Clear existing data
    db.query(models.Liability).delete()
    db.query(models.SCPI).delete()
    db.query(models.Asset).delete()
    db.query(models.PatrimoineHistory).delete()
    db.commit()

    # --- Assets ---
    assets_to_create = [
        schemas.AssetCreate(name="Livret A", value=11056, annual_interest=700, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Liquidité", value=11056)]),
        schemas.AssetCreate(name="Livret Grand Format", value=10, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Liquidité", value=10)]),
        schemas.AssetCreate(name="BforBank Livret", value=13, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Liquidité", value=13)]),
        schemas.AssetCreate(name="BforBank Assurance Vie", value=3338, annual_interest=49, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Fonds sécurisés", value=3338)]),
        schemas.AssetCreate(name="Linxea Avenir 2", value=3834, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Fonds sécurisés", value=3834)]),
        schemas.AssetCreate(name="October", value=138, annual_interest=None, notes="A stopper", update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Prêts participatifs", value=138)]),
        schemas.AssetCreate(name="Prexem", value=59, annual_interest=None, notes="A stopper", update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Prêts participatifs", value=59)]),
        schemas.AssetCreate(name="Mintos", value=14008, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Prêts participatifs", value=14008)]),
        schemas.AssetCreate(name="Lendosphere", value=1105, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Prêts participatifs", value=1105)]),
        schemas.AssetCreate(name="Trading 212", value=26336, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Bourse", value=26336)]),
        schemas.AssetCreate(name="BOURSE DIRECT PEA", value=85652, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Bourse", value=85652)]),
        schemas.AssetCreate(name="BOURSE DIRECT PME", value=2702, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Bourse", value=2702)]),
        schemas.AssetCreate(name="Trade Republic Bourse", value=22016, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Bourse", value=1120), schemas.CategoryValue(name="Fonds sécurisés", value=20896)]),
        schemas.AssetCreate(name="Bitpanda Crypto", value=22321, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Crypto-Actifs", value=22321)]),
        schemas.AssetCreate(name="Finst", value=17670, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Crypto-Actifs", value=17670)]),
        schemas.AssetCreate(name="Metamask", value=636, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Crypto-Actifs", value=636)]),
        schemas.AssetCreate(name="PER Linxea", value=27983, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Fonds sécurisés", value=27983)]),
        schemas.AssetCreate(name="Anaxago Immobilier", value=6610, annual_interest=849, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Immobilier", value=6610)]),
        schemas.AssetCreate(name="RealtT", value=1413, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name="Immobilier", value=1413)]),
    ]

    for asset_data in assets_to_create:
        asset = models.Asset(name=asset_data.name, value=asset_data.value, annual_interest=asset_data.annual_interest, notes=asset_data.notes, update_date=asset_data.update_date)
        db.add(asset)
        db.flush() # Flush to get db_asset.id

        for category_value in asset_data.categories:
            category = db.query(models.Category).filter(models.Category.name == category_value.name).first()
            if not category:
                category = models.Category(name=category_value.name)
                db.add(category)
                db.flush() # Flush to get category.id

            asset_category = models.AssetCategory(asset_id=asset.id, category_id=category.id, value=category_value.value)
            db.add(asset_category)

    # --- SCPI ---
    scpi_to_create = [
        {
            "name": "EPARGNE FONCIERE",
            "price_per_share": 619.75,
            "number_of_shares": 38,
            "total_value": 23550.5,
            "update_date": datetime.date(2025, 7, 1)
        },
        {
            "name": "PFO2",
            "price_per_share": 150.06,
            "number_of_shares": 150,
            "total_value": 22509,
            "update_date": datetime.date(2025, 7, 1)
        },
        {
            "name": "AESTIAM PIERRE RENDEMENT",
            "price_per_share": 829.8,
            "number_of_shares": 30,
            "total_value": 24894,
            "update_date": datetime.date(2025, 7, 1)
        },
        {
            "name": "Moniwan : LF OPPORTUNITE IMMO",
            "price_per_share": 184.73,
            "number_of_shares": 57,
            "total_value": 10529.61,
            "update_date": datetime.date(2025, 7, 1)
        }
    ]

    for scpi_data in scpi_to_create:
        scpi = models.SCPI(**scpi_data)
        db.add(scpi)

    # --- Liabilities ---
    liabilities_to_create = [
        {
            "name": "Emprunt immobilier",
            "initial_amount": 60000,  # Estimation basée sur le capital restant
            "remaining_capital": 49592,
            "interest_rate": 2.2,
            "end_date": datetime.date(2035, 11, 5),
            "monthly_payment": 450,  # Estimation basée sur durée et taux
            "asset_id": None  # Pas lié à un actif spécifique
        }
    ]

    for liability_data in liabilities_to_create:
        liability = models.Liability(**liability_data)
        db.add(liability)

    # --- Patrimoine History ---
    # Add historical net worth data here following this pattern:
    patrimoine_history_to_create = [
        schemas.PatrimoineHistoryCreate(
            date=datetime.date(2024, 1, 1),
            net_patrimoine=150000
        ),
        schemas.PatrimoineHistoryCreate(
            date=datetime.date(2024, 7, 1),
            net_patrimoine=160000
        ),
        schemas.PatrimoineHistoryCreate(
            date=datetime.date(2025, 1, 1),
            net_patrimoine=170000
        ),
        schemas.PatrimoineHistoryCreate(
            date=datetime.date(2025, 7, 1),
            net_patrimoine=180000
        ),
    ]

    for history_data in patrimoine_history_to_create:
        history = models.PatrimoineHistory(**history_data.model_dump())
        db.add(history)

    db.commit()

    # Calculate and display totals
    total_scpi_value = sum(scpi_data["total_value"] for scpi_data in scpi_to_create)
    total_liabilities = sum(liability_data["remaining_capital"] for liability_data in liabilities_to_create)
    print("Database has been seeded successfully.")
    print(f"Assets created: {len(assets_to_create)}")
    print(f"SCPI created: {len(scpi_to_create)}")
    print(f"Total SCPI value: {total_scpi_value:,.2f} €")
    print(f"Liabilities created: {len(liabilities_to_create)}")
    print(f"Total liabilities: {total_liabilities:,.2f} €")
    print(f"Patrimoine history entries: {len(patrimoine_history_to_create)}")

    db.close()
    engine.dispose()

if __name__ == "__main__":
    seed_data()
