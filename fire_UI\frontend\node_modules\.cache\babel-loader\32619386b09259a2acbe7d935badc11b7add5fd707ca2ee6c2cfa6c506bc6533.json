{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\Liabilities.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LiabilityForm = ({\n  liability,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: (liability === null || liability === void 0 ? void 0 : liability.name) || '',\n    initial_amount: (liability === null || liability === void 0 ? void 0 : liability.initial_amount) || 0,\n    remaining_capital: (liability === null || liability === void 0 ? void 0 : liability.remaining_capital) || 0,\n    interest_rate: (liability === null || liability === void 0 ? void 0 : liability.interest_rate) || 0,\n    monthly_payment: (liability === null || liability === void 0 ? void 0 : liability.monthly_payment) || 0,\n    end_date: liability !== null && liability !== void 0 && liability.end_date ? new Date(liability.end_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n  });\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const method = liability !== null && liability !== void 0 && liability.id ? 'put' : 'post';\n    const url = liability !== null && liability !== void 0 && liability.id ? `/liabilities/${liability.id}` : '/liabilities';\n    try {\n      await apiClient[method](url, formData);\n      onSave();\n    } catch (error) {\n      console.error(\"Erreur lors de la sauvegarde de l'emprunt\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Nom\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        value: formData.name,\n        onChange: e => setFormData({\n          ...formData,\n          name: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Capital Restant D\\xFB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.remaining_capital,\n        onChange: e => setFormData({\n          ...formData,\n          remaining_capital: parseFloat(e.target.value)\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Mensualit\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.monthly_payment,\n        onChange: e => setFormData({\n          ...formData,\n          monthly_payment: parseFloat(e.target.value)\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Taux d'int\\xE9r\\xEAt (%)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"0.01\",\n        value: formData.interest_rate,\n        onChange: e => setFormData({\n          ...formData,\n          interest_rate: parseFloat(e.target.value)\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Date de fin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"date\",\n        value: formData.end_date,\n        onChange: e => setFormData({\n          ...formData,\n          end_date: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"primary\",\n      type: \"submit\",\n      children: \"Sauvegarder\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"secondary\",\n      onClick: onCancel,\n      className: \"ms-2\",\n      children: \"Annuler\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 9\n  }, this);\n};\n_s(LiabilityForm, \"7zqjyBEieut4saxQ+QQy3t1BcQA=\");\n_c = LiabilityForm;\nconst Liabilities = () => {\n  _s2();\n  const [liabilities, setLiabilities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedLiability, setSelectedLiability] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchLiabilities = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/liabilities').then(response => {\n      setLiabilities(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('Liabilities API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchLiabilities(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des emprunts.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchLiabilities();\n  }, []);\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedLiability(null);\n    fetchLiabilities();\n  };\n  const handleDelete = async id => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet emprunt ?\")) {\n      try {\n        await apiClient.delete(`/liabilities/${id}`);\n        fetchLiabilities();\n      } catch (error) {\n        console.error(\"Erreur lors de la suppression de l'emprunt\", error);\n      }\n    }\n  };\n  if (loading && !showModal) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 37\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchLiabilities,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Mes Emprunts (Passifs)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => {\n          setSelectedLiability({});\n          setShowModal(true);\n        },\n        children: \"Ajouter un Emprunt\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table table-striped table-hover\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-dark\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Nom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Capital Restant D\\xFB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Mensualit\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Taux\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Date de Fin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: liabilities.map(liability => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: liability.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(liability.remaining_capital)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(liability.monthly_payment)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: [liability.interest_rate, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: new Date(liability.end_date).toLocaleDateString('fr-FR')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => {\n                setSelectedLiability(liability);\n                setShowModal(true);\n              },\n              children: \"Modifier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), ' ', /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: () => handleDelete(liability.id),\n              children: \"Supprimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, liability.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [selectedLiability !== null && selectedLiability !== void 0 && selectedLiability.id ? 'Modifier' : 'Ajouter', \" un Emprunt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(LiabilityForm, {\n          liability: selectedLiability,\n          onSave: handleSave,\n          onCancel: () => setShowModal(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s2(Liabilities, \"sOqg+koVLpJ107wiX6v+b5ChLr4=\");\n_c2 = Liabilities;\nexport default Liabilities;\nvar _c, _c2;\n$RefreshReg$(_c, \"LiabilityForm\");\n$RefreshReg$(_c2, \"Liabilities\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Modal", "<PERSON><PERSON>", "Form", "jsxDEV", "_jsxDEV", "LiabilityForm", "liability", "onSave", "onCancel", "_s", "formData", "setFormData", "name", "initial_amount", "remaining_capital", "interest_rate", "monthly_payment", "end_date", "Date", "toISOString", "split", "handleSubmit", "e", "preventDefault", "method", "id", "url", "error", "console", "onSubmit", "children", "Group", "className", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "type", "value", "onChange", "target", "required", "parseFloat", "step", "variant", "onClick", "_c", "Liabilities", "_s2", "liabilities", "setLiabilities", "loading", "setLoading", "setError", "showModal", "setShowModal", "selectedLiability", "setSelectedLiability", "retryCount", "setRetryCount", "fetchLiabilities", "get", "then", "response", "data", "catch", "prev", "setTimeout", "handleSave", "handleDelete", "window", "confirm", "delete", "map", "Intl", "NumberFormat", "style", "currency", "format", "toLocaleDateString", "size", "show", "onHide", "Header", "closeButton", "Title", "Body", "_c2", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/Liabilities.tsx"], "sourcesContent": ["import React, { useEffect, useState, FormEvent } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form } from 'react-bootstrap';\n\ninterface Liability {\n  id: number;\n  name: string;\n  initial_amount: number;\n  remaining_capital: number;\n  interest_rate: number;\n  end_date: string;\n  monthly_payment: number;\n}\n\nconst LiabilityForm: React.FC<{ liability: Partial<Liability> | null, onSave: () => void, onCancel: () => void }> = ({ liability, onSave, onCancel }) => {\n    const [formData, setFormData] = useState({\n        name: liability?.name || '',\n        initial_amount: liability?.initial_amount || 0,\n        remaining_capital: liability?.remaining_capital || 0,\n        interest_rate: liability?.interest_rate || 0,\n        monthly_payment: liability?.monthly_payment || 0,\n        end_date: liability?.end_date ? new Date(liability.end_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n    });\n\n    const handleSubmit = async (e: FormEvent) => {\n        e.preventDefault();\n        const method = liability?.id ? 'put' : 'post';\n        const url = liability?.id ? `/liabilities/${liability.id}` : '/liabilities';\n        \n        try {\n            await apiClient[method](url, formData);\n            onSave();\n        } catch (error) {\n            console.error(\"Erreur lors de la sauvegarde de l'emprunt\", error);\n        }\n    };\n\n    return (\n        <Form onSubmit={handleSubmit}>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Nom</Form.Label>\n                <Form.Control type=\"text\" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Capital Restant Dû</Form.Label>\n                <Form.Control type=\"number\" value={formData.remaining_capital} onChange={e => setFormData({ ...formData, remaining_capital: parseFloat(e.target.value) })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Mensualité</Form.Label>\n                <Form.Control type=\"number\" value={formData.monthly_payment} onChange={e => setFormData({ ...formData, monthly_payment: parseFloat(e.target.value) })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Taux d'intérêt (%)</Form.Label>\n                <Form.Control type=\"number\" step=\"0.01\" value={formData.interest_rate} onChange={e => setFormData({ ...formData, interest_rate: parseFloat(e.target.value) })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Date de fin</Form.Label>\n                <Form.Control type=\"date\" value={formData.end_date} onChange={e => setFormData({ ...formData, end_date: e.target.value })} required />\n            </Form.Group>\n            <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\n            <Button variant=\"secondary\" onClick={onCancel} className=\"ms-2\">Annuler</Button>\n        </Form>\n    );\n};\n\n\nconst Liabilities: React.FC = () => {\n  const [liabilities, setLiabilities] = useState<Liability[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedLiability, setSelectedLiability] = useState<Partial<Liability> | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchLiabilities = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/liabilities')\n      .then(response => {\n        setLiabilities(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('Liabilities API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchLiabilities(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des emprunts.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchLiabilities();\n  }, []);\n\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedLiability(null);\n    fetchLiabilities();\n  };\n\n  const handleDelete = async (id: number) => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet emprunt ?\")) {\n        try {\n            await apiClient.delete(`/liabilities/${id}`);\n            fetchLiabilities();\n        } catch (error) {\n            console.error(\"Erreur lors de la suppression de l'emprunt\", error);\n        }\n    }\n  };\n\n  if (loading && !showModal) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchLiabilities}>\n        Réessayer\n      </button>\n    </div>\n  );\n\n  return (\n    <div>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1>Mes Emprunts (Passifs)</h1>\n        <Button variant=\"primary\" onClick={() => { setSelectedLiability({}); setShowModal(true); }}>Ajouter un Emprunt</Button>\n      </div>\n      <table className=\"table table-striped table-hover\">\n        <thead className=\"table-dark\">\n          <tr>\n            <th>Nom</th>\n            <th className=\"text-end\">Capital Restant Dû</th>\n            <th className=\"text-end\">Mensualité</th>\n            <th className=\"text-center\">Taux</th>\n            <th className=\"text-center\">Date de Fin</th>\n            <th className=\"text-center\">Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          {liabilities.map(liability => (\n            <tr key={liability.id}>\n              <td>{liability.name}</td>\n              <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(liability.remaining_capital)}</td>\n              <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(liability.monthly_payment)}</td>\n              <td className=\"text-center\">{liability.interest_rate}%</td>\n              <td className=\"text-center\">{new Date(liability.end_date).toLocaleDateString('fr-FR')}</td>\n              <td className=\"text-center\">\n                <Button variant=\"outline-primary\" size=\"sm\" onClick={() => { setSelectedLiability(liability); setShowModal(true); }}>Modifier</Button>{' '}\n                <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(liability.id)}>Supprimer</Button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>{selectedLiability?.id ? 'Modifier' : 'Ajouter'} un Emprunt</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <LiabilityForm liability={selectedLiability} onSave={handleSave} onCancel={() => setShowModal(false)} />\n        </Modal.Body>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Liabilities;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAC7D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYtD,MAAMC,aAA2G,GAAGA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC;IACrCc,IAAI,EAAE,CAAAN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,IAAI,KAAI,EAAE;IAC3BC,cAAc,EAAE,CAAAP,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,cAAc,KAAI,CAAC;IAC9CC,iBAAiB,EAAE,CAAAR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,iBAAiB,KAAI,CAAC;IACpDC,aAAa,EAAE,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,aAAa,KAAI,CAAC;IAC5CC,eAAe,EAAE,CAAAV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,eAAe,KAAI,CAAC;IAChDC,QAAQ,EAAEX,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,QAAQ,GAAG,IAAIC,IAAI,CAACZ,SAAS,CAACW,QAAQ,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACpI,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,MAAOC,CAAY,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAGlB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEmB,EAAE,GAAG,KAAK,GAAG,MAAM;IAC7C,MAAMC,GAAG,GAAGpB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEmB,EAAE,GAAG,gBAAgBnB,SAAS,CAACmB,EAAE,EAAE,GAAG,cAAc;IAE3E,IAAI;MACA,MAAM1B,SAAS,CAACyB,MAAM,CAAC,CAACE,GAAG,EAAEhB,QAAQ,CAAC;MACtCH,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACrE;EACJ,CAAC;EAED,oBACIvB,OAAA,CAACF,IAAI;IAAC2B,QAAQ,EAAER,YAAa;IAAAS,QAAA,gBACzB1B,OAAA,CAACF,IAAI,CAAC6B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB1B,OAAA,CAACF,IAAI,CAAC+B,KAAK;QAAAH,QAAA,EAAC;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5BjC,OAAA,CAACF,IAAI,CAACoC,OAAO;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAE9B,QAAQ,CAACE,IAAK;QAAC6B,QAAQ,EAAEnB,CAAC,IAAIX,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEE,IAAI,EAAEU,CAAC,CAACoB,MAAM,CAACF;QAAM,CAAC,CAAE;QAACG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtH,CAAC,eACbjC,OAAA,CAACF,IAAI,CAAC6B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB1B,OAAA,CAACF,IAAI,CAAC+B,KAAK;QAAAH,QAAA,EAAC;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3CjC,OAAA,CAACF,IAAI,CAACoC,OAAO;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE9B,QAAQ,CAACI,iBAAkB;QAAC2B,QAAQ,EAAEnB,CAAC,IAAIX,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEI,iBAAiB,EAAE8B,UAAU,CAACtB,CAAC,CAACoB,MAAM,CAACF,KAAK;QAAE,CAAC,CAAE;QAACG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9J,CAAC,eACbjC,OAAA,CAACF,IAAI,CAAC6B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB1B,OAAA,CAACF,IAAI,CAAC+B,KAAK;QAAAH,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACnCjC,OAAA,CAACF,IAAI,CAACoC,OAAO;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE9B,QAAQ,CAACM,eAAgB;QAACyB,QAAQ,EAAEnB,CAAC,IAAIX,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEM,eAAe,EAAE4B,UAAU,CAACtB,CAAC,CAACoB,MAAM,CAACF,KAAK;QAAE,CAAC,CAAE;QAACG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1J,CAAC,eACbjC,OAAA,CAACF,IAAI,CAAC6B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB1B,OAAA,CAACF,IAAI,CAAC+B,KAAK;QAAAH,QAAA,EAAC;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3CjC,OAAA,CAACF,IAAI,CAACoC,OAAO;QAACC,IAAI,EAAC,QAAQ;QAACM,IAAI,EAAC,MAAM;QAACL,KAAK,EAAE9B,QAAQ,CAACK,aAAc;QAAC0B,QAAQ,EAAEnB,CAAC,IAAIX,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEK,aAAa,EAAE6B,UAAU,CAACtB,CAAC,CAACoB,MAAM,CAACF,KAAK;QAAE,CAAC,CAAE;QAACG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClK,CAAC,eACbjC,OAAA,CAACF,IAAI,CAAC6B,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB1B,OAAA,CAACF,IAAI,CAAC+B,KAAK;QAAAH,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACpCjC,OAAA,CAACF,IAAI,CAACoC,OAAO;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAE9B,QAAQ,CAACO,QAAS;QAACwB,QAAQ,EAAEnB,CAAC,IAAIX,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEO,QAAQ,EAAEK,CAAC,CAACoB,MAAM,CAACF;QAAM,CAAC,CAAE;QAACG,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9H,CAAC,eACbjC,OAAA,CAACH,MAAM;MAAC6C,OAAO,EAAC,SAAS;MAACP,IAAI,EAAC,QAAQ;MAAAT,QAAA,EAAC;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC5DjC,OAAA,CAACH,MAAM;MAAC6C,OAAO,EAAC,WAAW;MAACC,OAAO,EAAEvC,QAAS;MAACwB,SAAS,EAAC,MAAM;MAAAF,QAAA,EAAC;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9E,CAAC;AAEf,CAAC;AAAC5B,EAAA,CAjDIJ,aAA2G;AAAA2C,EAAA,GAA3G3C,aAA2G;AAoDjH,MAAM4C,WAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAc,EAAE,CAAC;EAC/D,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAE4B,QAAQ,CAAC,GAAGzD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7D,QAAQ,CAA4B,IAAI,CAAC;EAC3F,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMgE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BR,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEdxD,SAAS,CAACgE,GAAG,CAAC,cAAc,CAAC,CAC1BC,IAAI,CAACC,QAAQ,IAAI;MAChBb,cAAc,CAACa,QAAQ,CAACC,IAAI,CAAC;MAC7BZ,UAAU,CAAC,KAAK,CAAC;MACjBO,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDM,KAAK,CAACxC,KAAK,IAAI;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,IAAIiC,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAMP,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC;MAC5C,CAAC,MAAM;QACLP,QAAQ,CAAC,8CAA8C,CAAC;QACxDD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDzD,SAAS,CAAC,MAAM;IACdiE,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvBb,YAAY,CAAC,KAAK,CAAC;IACnBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BG,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED,MAAMS,YAAY,GAAG,MAAO9C,EAAU,IAAK;IACzC,IAAI+C,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MACpE,IAAI;QACA,MAAM1E,SAAS,CAAC2E,MAAM,CAAC,gBAAgBjD,EAAE,EAAE,CAAC;QAC5CqC,gBAAgB,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACtE;IACJ;EACF,CAAC;EAED,IAAI0B,OAAO,IAAI,CAACG,SAAS,EAAE,oBAAOpD,OAAA;IAAA0B,QAAA,EAAG;EAAa;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACtD,IAAIV,KAAK,EAAE,oBACTvB,OAAA;IAAA0B,QAAA,gBACE1B,OAAA;MAAG4B,SAAS,EAAC,aAAa;MAAAF,QAAA,EAAEH;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtCjC,OAAA;MAAQ4B,SAAS,EAAC,iBAAiB;MAACe,OAAO,EAAEe,gBAAiB;MAAAhC,QAAA,EAAC;IAE/D;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAGR,oBACEjC,OAAA;IAAA0B,QAAA,gBACE1B,OAAA;MAAK4B,SAAS,EAAC,wDAAwD;MAAAF,QAAA,gBACrE1B,OAAA;QAAA0B,QAAA,EAAI;MAAsB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/BjC,OAAA,CAACH,MAAM;QAAC6C,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAM;UAAEY,oBAAoB,CAAC,CAAC,CAAC,CAAC;UAAEF,YAAY,CAAC,IAAI,CAAC;QAAE,CAAE;QAAA3B,QAAA,EAAC;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpH,CAAC,eACNjC,OAAA;MAAO4B,SAAS,EAAC,iCAAiC;MAAAF,QAAA,gBAChD1B,OAAA;QAAO4B,SAAS,EAAC,YAAY;QAAAF,QAAA,eAC3B1B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAA0B,QAAA,EAAI;UAAG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZjC,OAAA;YAAI4B,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDjC,OAAA;YAAI4B,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCjC,OAAA;YAAI4B,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCjC,OAAA;YAAI4B,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CjC,OAAA;YAAI4B,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRjC,OAAA;QAAA0B,QAAA,EACGqB,WAAW,CAACwB,GAAG,CAACrE,SAAS,iBACxBF,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAA0B,QAAA,EAAKxB,SAAS,CAACM;UAAI;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzBjC,OAAA;YAAI4B,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAI8C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAAC1E,SAAS,CAACQ,iBAAiB;UAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1IjC,OAAA;YAAI4B,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAI8C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAAC1E,SAAS,CAACU,eAAe;UAAC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxIjC,OAAA;YAAI4B,SAAS,EAAC,aAAa;YAAAF,QAAA,GAAExB,SAAS,CAACS,aAAa,EAAC,GAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DjC,OAAA;YAAI4B,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAE,IAAIZ,IAAI,CAACZ,SAAS,CAACW,QAAQ,CAAC,CAACgE,kBAAkB,CAAC,OAAO;UAAC;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3FjC,OAAA;YAAI4B,SAAS,EAAC,aAAa;YAAAF,QAAA,gBACzB1B,OAAA,CAACH,MAAM;cAAC6C,OAAO,EAAC,iBAAiB;cAACoC,IAAI,EAAC,IAAI;cAACnC,OAAO,EAAEA,CAAA,KAAM;gBAAEY,oBAAoB,CAACrD,SAAS,CAAC;gBAAEmD,YAAY,CAAC,IAAI,CAAC;cAAE,CAAE;cAAA3B,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAC1IjC,OAAA,CAACH,MAAM;cAAC6C,OAAO,EAAC,gBAAgB;cAACoC,IAAI,EAAC,IAAI;cAACnC,OAAO,EAAEA,CAAA,KAAMwB,YAAY,CAACjE,SAAS,CAACmB,EAAE,CAAE;cAAAK,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC;QAAA,GATE/B,SAAS,CAACmB,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUjB,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAERjC,OAAA,CAACJ,KAAK;MAACmF,IAAI,EAAE3B,SAAU;MAAC4B,MAAM,EAAEA,CAAA,KAAM3B,YAAY,CAAC,KAAK,CAAE;MAAA3B,QAAA,gBACxD1B,OAAA,CAACJ,KAAK,CAACqF,MAAM;QAACC,WAAW;QAAAxD,QAAA,eACvB1B,OAAA,CAACJ,KAAK,CAACuF,KAAK;UAAAzD,QAAA,GAAE4B,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEjC,EAAE,GAAG,UAAU,GAAG,SAAS,EAAC,aAAW;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eACfjC,OAAA,CAACJ,KAAK,CAACwF,IAAI;QAAA1D,QAAA,eACT1B,OAAA,CAACC,aAAa;UAACC,SAAS,EAAEoD,iBAAkB;UAACnD,MAAM,EAAE+D,UAAW;UAAC9D,QAAQ,EAAEA,CAAA,KAAMiD,YAAY,CAAC,KAAK;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACa,GAAA,CA1GID,WAAqB;AAAAwC,GAAA,GAArBxC,WAAqB;AA4G3B,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAyC,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}