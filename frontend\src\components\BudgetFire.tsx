import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { Modal, Button, Form, Alert, ProgressBar } from 'react-bootstrap';

interface BudgetCategory {
  id: number;
  nom: string;
  budget_annuel: number;
  description: string;
  ordre_affichage: number;
}

interface DepenseReelle {
  id: number;
  categorie_id: number;
  montant: number;
  date_depense: string;
  description: string;
  mois: number;
  annee: number;
}

interface BudgetAnalysis {
  categorie_id: number;
  nom_categorie: string;
  budget_annuel: number;
  depenses_mois_actuel: number;
  depenses_annee_actuelle: number;
  ecart_euros: number;
  ecart_pourcentage: number;
  projection_annuelle: number;
  statut: string;
}

interface BudgetSummary {
  total_budget_annuel: number;
  total_depenses_annee: number;
  total_ecart_euros: number;
  total_ecart_pourcentage: number;
  retrait_brut_necessaire: number;
  impact_date_fire: string;
}

const BudgetCategoryForm: React.FC<{
  category: Partial<BudgetCategory> | null,
  onSave: () => void,
  onCancel: () => void
}> = ({ category, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    nom: category?.nom || '',
    budget_annuel: category?.budget_annuel || 0,
    description: category?.description || '',
    ordre_affichage: category?.ordre_affichage || 0,
  });

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    const method = category?.id ? 'put' : 'post';
    const url = category?.id ? `/budget/categories/${category.id}` : '/budget/categories';

    console.log('Submitting budget category:', { method, url, formData });

    try {
      const response = await apiClient[method](url, formData);
      console.log('Budget category saved successfully:', response.data);
      onSave();
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde de la catégorie", error);
      console.error("Error details:", error.response?.data);
      alert(`Erreur: ${error.response?.data?.detail || error.message}`);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Nom de la catégorie</Form.Label>
        <Form.Control
          type="text"
          value={formData.nom}
          onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
          required
          placeholder="Ex: Alimentation, Assurance Maison..."
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Budget annuel (€)</Form.Label>
        <Form.Control
          type="number"
          step="0.01"
          value={formData.budget_annuel}
          onChange={(e) => setFormData({ ...formData, budget_annuel: parseFloat(e.target.value) || 0 })}
          required
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Description</Form.Label>
        <Form.Control
          type="text"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Description de la catégorie..."
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Ordre d'affichage</Form.Label>
        <Form.Control
          type="number"
          value={formData.ordre_affichage}
          onChange={(e) => setFormData({ ...formData, ordre_affichage: parseInt(e.target.value) || 0 })}
          placeholder="Ordre d'affichage (1, 2, 3...)"
        />
        <Form.Text className="text-muted">
          Plus le nombre est petit, plus la catégorie apparaîtra en haut de la liste
        </Form.Text>
      </Form.Group>

      <div className="d-flex justify-content-end gap-2">
        <Button variant="secondary" onClick={onCancel}>Annuler</Button>
        <Button variant="primary" type="submit">Sauvegarder</Button>
      </div>
    </Form>
  );
};

const DepenseForm: React.FC<{
  depense: Partial<DepenseReelle> | null,
  categories: BudgetCategory[],
  onSave: () => void,
  onCancel: () => void
}> = ({ depense, categories, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    categorie_id: depense?.categorie_id || (categories[0]?.id || 1),
    montant: depense?.montant || 0,
    date_depense: depense?.date_depense || new Date().toISOString().split('T')[0],
    description: depense?.description || '',
  });

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    const method = depense?.id ? 'put' : 'post';
    const url = depense?.id ? `/budget/depenses/${depense.id}` : '/budget/depenses';
    
    console.log('Submitting expense:', { method, url, formData });
    
    try {
      const response = await apiClient[method](url, formData);
      console.log('Expense saved successfully:', response.data);
      onSave();
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde de la dépense", error);
      console.error("Error details:", error.response?.data);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Catégorie</Form.Label>
        <Form.Select
          value={formData.categorie_id}
          onChange={(e) => setFormData({ ...formData, categorie_id: parseInt(e.target.value) })}
          required
        >
          {categories.map(cat => (
            <option key={cat.id} value={cat.id}>{cat.nom}</option>
          ))}
        </Form.Select>
      </Form.Group>
      
      <Form.Group className="mb-3">
        <Form.Label>Montant (€)</Form.Label>
        <Form.Control
          type="number"
          step="0.01"
          value={formData.montant}
          onChange={(e) => setFormData({ ...formData, montant: parseFloat(e.target.value) || 0 })}
          required
        />
      </Form.Group>
      
      <Form.Group className="mb-3">
        <Form.Label>Date</Form.Label>
        <Form.Control
          type="date"
          value={formData.date_depense}
          onChange={(e) => setFormData({ ...formData, date_depense: e.target.value })}
          required
        />
      </Form.Group>
      
      <Form.Group className="mb-3">
        <Form.Label>Description (optionnelle)</Form.Label>
        <Form.Control
          type="text"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Description de la dépense..."
        />
      </Form.Group>
      
      <div className="d-flex justify-content-end gap-2">
        <Button variant="secondary" onClick={onCancel}>Annuler</Button>
        <Button variant="primary" type="submit">Sauvegarder</Button>
      </div>
    </Form>
  );
};

const BudgetFire: React.FC = () => {
  const [categories, setCategories] = useState<BudgetCategory[]>([]);
  const [analyses, setAnalyses] = useState<BudgetAnalysis[]>([]);
  const [summary, setSummary] = useState<BudgetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showExpenseModal, setShowExpenseModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [selectedDepense, setSelectedDepense] = useState<Partial<DepenseReelle> | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Partial<BudgetCategory> | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [showCategoriesTable, setShowCategoriesTable] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [categoriesRes, analysesRes, summaryRes] = await Promise.all([
        apiClient.get('/budget/categories'),
        apiClient.get('/budget/analysis'),
        apiClient.get('/budget/summary')
      ]);
      
      setCategories(categoriesRes.data);
      setAnalyses(analysesRes.data);
      setSummary(summaryRes.data);
      setLoading(false);
      setRetryCount(0);
    } catch (error: any) {
      console.error('Budget API error:', error);
      
      if (retryCount < 2) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchData(), 1000);
      } else {
        setError('Erreur lors de la récupération des données budgétaires.');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleExpenseSave = () => {
    setShowExpenseModal(false);
    setSelectedDepense(null);
    fetchData();
  };

  const handleCategorySave = () => {
    setShowCategoryModal(false);
    setSelectedCategory(null);
    fetchData();
  };

  const handleDeleteCategory = async (categoryId: number, categoryName: string) => {
    // Vérifier s'il y a des dépenses associées
    const hasExpenses = analyses.some(analysis =>
      analysis.categorie_id === categoryId && analysis.depenses_annee_actuelle > 0
    );

    if (hasExpenses) {
      alert(`Impossible de supprimer la catégorie "${categoryName}" car elle contient des dépenses. Supprimez d'abord toutes les dépenses de cette catégorie.`);
      return;
    }

    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${categoryName}" ? Cette action est irréversible.`)) {
      try {
        await apiClient.delete(`/budget/categories/${categoryId}`);
        fetchData();
      } catch (error: any) {
        console.error("Erreur lors de la suppression de la catégorie", error);
        alert(`Erreur lors de la suppression: ${error.response?.data?.detail || error.message}`);
      }
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  };

  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { 
      style: 'percent', 
      minimumFractionDigits: 1,
      maximumFractionDigits: 1 
    }).format(value / 100);
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case 'excellent': return 'success';
      case 'ok': return 'primary';
      case 'attention': return 'warning';
      case 'depassement': return 'danger';
      default: return 'secondary';
    }
  };

  const getStatusText = (statut: string) => {
    switch (statut) {
      case 'excellent': return 'Excellent';
      case 'ok': return 'OK';
      case 'attention': return 'Attention';
      case 'depassement': return 'Dépassement';
      default: return 'N/A';
    }
  };

  if (loading && !showExpenseModal && !showCategoryModal) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchData}>
        Réessayer
      </button>
    </div>
  );

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Budget & Dépenses FIRE</h1>
        <div className="d-flex gap-2">
          <Button
            variant="outline-secondary"
            onClick={() => setShowCategoriesTable(!showCategoriesTable)}
          >
            {showCategoriesTable ? 'Masquer' : 'Gérer'} Catégories
          </Button>
          <Button
            variant="success"
            onClick={() => { setSelectedCategory({}); setShowCategoryModal(true); }}
          >
            Nouvelle Catégorie
          </Button>
          <Button
            variant="primary"
            onClick={() => { setSelectedDepense({}); setShowExpenseModal(true); }}
          >
            Ajouter Dépense
          </Button>
        </div>
      </div>

      {/* Résumé global */}
      {summary && (
        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card text-white bg-primary">
              <div className="card-body text-center">
                <h5>Budget Annuel FIRE</h5>
                <h4>{formatCurrency(summary.total_budget_annuel)}</h4>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card text-white bg-info">
              <div className="card-body text-center">
                <h5>Dépenses Réalisées</h5>
                <h4>{formatCurrency(summary.total_depenses_annee)}</h4>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className={`card text-white ${summary.total_ecart_euros >= 0 ? 'bg-danger' : 'bg-success'}`}>
              <div className="card-body text-center">
                <h5>Écart Budget</h5>
                <h4>{formatCurrency(Math.abs(summary.total_ecart_euros))}</h4>
                <small>{summary.total_ecart_euros >= 0 ? 'Dépassement' : 'Économie'}</small>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card text-white bg-warning">
              <div className="card-body text-center">
                <h5>Retrait Brut Nécessaire</h5>
                <h4>{formatCurrency(summary.retrait_brut_necessaire)}</h4>
                <small>+30% impôts/PS</small>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Impact sur objectif FIRE */}
      {summary && (
        <Alert variant={summary.total_ecart_euros >= 0 ? 'warning' : 'success'} className="mb-4">
          <Alert.Heading>Impact sur l'objectif FIRE 2038</Alert.Heading>
          <p className="mb-0">{summary.impact_date_fire}</p>
        </Alert>
      )}

      {/* Tableau de gestion des catégories */}
      {showCategoriesTable && (
        <div className="mb-4">
          <h3>Gestion des Catégories Budgétaires</h3>
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead className="table-secondary">
                <tr>
                  <th>Ordre</th>
                  <th>Nom</th>
                  <th className="text-end">Budget Annuel</th>
                  <th>Description</th>
                  <th className="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {categories.map(category => (
                  <tr key={category.id}>
                    <td>{category.ordre_affichage}</td>
                    <td><strong>{category.nom}</strong></td>
                    <td className="text-end">{formatCurrency(category.budget_annuel)}</td>
                    <td>{category.description}</td>
                    <td className="text-center">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="me-2"
                        onClick={() => { setSelectedCategory(category); setShowCategoryModal(true); }}
                      >
                        Modifier
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleDeleteCategory(category.id, category.nom)}
                      >
                        Supprimer
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="table-info">
                  <th colSpan={2}>TOTAL</th>
                  <th className="text-end">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0))}</th>
                  <th colSpan={2}></th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {/* Tableau détaillé par catégorie */}
      <div className="table-responsive">
        <table className="table table-striped table-hover">
          <thead className="table-dark">
            <tr>
              <th>Catégorie</th>
              <th className="text-end">Budget Annuel</th>
              <th className="text-end">Réalisé Année</th>
              <th className="text-end">Réalisé Mois</th>
              <th className="text-end">Écart €</th>
              <th className="text-end">Écart %</th>
              <th className="text-end">Projection</th>
              <th className="text-center">Statut</th>
              <th className="text-center">Progression</th>
            </tr>
          </thead>
          <tbody>
            {analyses.map(analysis => (
              <tr key={analysis.categorie_id}>
                <td><strong>{analysis.nom_categorie}</strong></td>
                <td className="text-end">{formatCurrency(analysis.budget_annuel)}</td>
                <td className="text-end">{formatCurrency(analysis.depenses_annee_actuelle)}</td>
                <td className="text-end">{formatCurrency(analysis.depenses_mois_actuel)}</td>
                <td className={`text-end ${analysis.ecart_euros >= 0 ? 'text-danger' : 'text-success'}`}>
                  {formatCurrency(analysis.ecart_euros)}
                </td>
                <td className={`text-end ${analysis.ecart_pourcentage >= 0 ? 'text-danger' : 'text-success'}`}>
                  {formatPercentage(analysis.ecart_pourcentage)}
                </td>
                <td className="text-end">{formatCurrency(analysis.projection_annuelle)}</td>
                <td className="text-center">
                  <span className={`badge bg-${getStatusColor(analysis.statut)}`}>
                    {getStatusText(analysis.statut)}
                  </span>
                </td>
                <td className="text-center" style={{ width: '120px' }}>
                  <ProgressBar
                    now={Math.min((analysis.depenses_annee_actuelle / analysis.budget_annuel) * 100, 100)}
                    variant={getStatusColor(analysis.statut)}
                    style={{ height: '8px' }}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal pour ajouter/modifier une dépense */}
      <Modal show={showExpenseModal} onHide={() => setShowExpenseModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedDepense?.id ? 'Modifier la dépense' : 'Ajouter une dépense'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <DepenseForm
            depense={selectedDepense}
            categories={categories}
            onSave={handleExpenseSave}
            onCancel={() => setShowExpenseModal(false)}
          />
        </Modal.Body>
      </Modal>

      {/* Modal pour ajouter/modifier une catégorie budgétaire */}
      <Modal show={showCategoryModal} onHide={() => setShowCategoryModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedCategory?.id ? `Modifier "${selectedCategory.nom}"` : 'Nouvelle catégorie budgétaire'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <BudgetCategoryForm
            category={selectedCategory}
            onSave={handleCategorySave}
            onCancel={() => setShowCategoryModal(false)}
          />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default BudgetFire;
