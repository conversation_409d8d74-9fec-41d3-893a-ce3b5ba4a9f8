import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { <PERSON>dal, Button, Form, Alert, ProgressBar } from 'react-bootstrap';

interface BudgetCategory {
  id: number;
  nom: string;
  budget_annuel: number;
  description: string;
  ordre_affichage: number;
}

interface DepenseReelle {
  id: number;
  categorie_id: number;
  montant: number;
  date_depense: string;
  description: string;
  mois: number;
  annee: number;
}

interface BudgetAnalysis {
  categorie_id: number;
  nom_categorie: string;
  budget_annuel: number;
  depenses_mois_actuel: number;
  depenses_annee_actuelle: number;
  ecart_euros: number;
  ecart_pourcentage: number;
  projection_annuelle: number;
  statut: string;
}

interface BudgetSummary {
  total_budget_annuel: number;
  total_depenses_annee: number;
  total_ecart_euros: number;
  total_ecart_pourcentage: number;
  retrait_brut_necessaire: number;
  impact_date_fire: string;
}

const DepenseForm: React.FC<{ 
  depense: Partial<DepenseReelle> | null,
  categories: BudgetCategory[],
  onSave: () => void, 
  onCancel: () => void 
}> = ({ depense, categories, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    categorie_id: depense?.categorie_id || (categories[0]?.id || 1),
    montant: depense?.montant || 0,
    date_depense: depense?.date_depense || new Date().toISOString().split('T')[0],
    description: depense?.description || '',
  });

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    const method = depense?.id ? 'put' : 'post';
    const url = depense?.id ? `/budget/depenses/${depense.id}` : '/budget/depenses';
    
    console.log('Submitting expense:', { method, url, formData });
    
    try {
      const response = await apiClient[method](url, formData);
      console.log('Expense saved successfully:', response.data);
      onSave();
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde de la dépense", error);
      console.error("Error details:", error.response?.data);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Catégorie</Form.Label>
        <Form.Select
          value={formData.categorie_id}
          onChange={(e) => setFormData({ ...formData, categorie_id: parseInt(e.target.value) })}
          required
        >
          {categories.map(cat => (
            <option key={cat.id} value={cat.id}>{cat.nom}</option>
          ))}
        </Form.Select>
      </Form.Group>
      
      <Form.Group className="mb-3">
        <Form.Label>Montant (€)</Form.Label>
        <Form.Control
          type="number"
          step="0.01"
          value={formData.montant}
          onChange={(e) => setFormData({ ...formData, montant: parseFloat(e.target.value) || 0 })}
          required
        />
      </Form.Group>
      
      <Form.Group className="mb-3">
        <Form.Label>Date</Form.Label>
        <Form.Control
          type="date"
          value={formData.date_depense}
          onChange={(e) => setFormData({ ...formData, date_depense: e.target.value })}
          required
        />
      </Form.Group>
      
      <Form.Group className="mb-3">
        <Form.Label>Description (optionnelle)</Form.Label>
        <Form.Control
          type="text"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Description de la dépense..."
        />
      </Form.Group>
      
      <div className="d-flex justify-content-end gap-2">
        <Button variant="secondary" onClick={onCancel}>Annuler</Button>
        <Button variant="primary" type="submit">Sauvegarder</Button>
      </div>
    </Form>
  );
};

const BudgetFire: React.FC = () => {
  const [categories, setCategories] = useState<BudgetCategory[]>([]);
  const [analyses, setAnalyses] = useState<BudgetAnalysis[]>([]);
  const [summary, setSummary] = useState<BudgetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedDepense, setSelectedDepense] = useState<Partial<DepenseReelle> | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [categoriesRes, analysesRes, summaryRes] = await Promise.all([
        apiClient.get('/budget/categories'),
        apiClient.get('/budget/analysis'),
        apiClient.get('/budget/summary')
      ]);
      
      setCategories(categoriesRes.data);
      setAnalyses(analysesRes.data);
      setSummary(summaryRes.data);
      setLoading(false);
      setRetryCount(0);
    } catch (error: any) {
      console.error('Budget API error:', error);
      
      if (retryCount < 2) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchData(), 1000);
      } else {
        setError('Erreur lors de la récupération des données budgétaires.');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleSave = () => {
    setShowModal(false);
    setSelectedDepense(null);
    fetchData();
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  };

  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { 
      style: 'percent', 
      minimumFractionDigits: 1,
      maximumFractionDigits: 1 
    }).format(value / 100);
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case 'excellent': return 'success';
      case 'ok': return 'primary';
      case 'attention': return 'warning';
      case 'depassement': return 'danger';
      default: return 'secondary';
    }
  };

  const getStatusText = (statut: string) => {
    switch (statut) {
      case 'excellent': return 'Excellent';
      case 'ok': return 'OK';
      case 'attention': return 'Attention';
      case 'depassement': return 'Dépassement';
      default: return 'N/A';
    }
  };

  if (loading && !showModal) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchData}>
        Réessayer
      </button>
    </div>
  );

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Budget & Dépenses FIRE</h1>
        <Button variant="primary" onClick={() => { setSelectedDepense({}); setShowModal(true); }}>
          Ajouter une dépense
        </Button>
      </div>

      {/* Résumé global */}
      {summary && (
        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card text-white bg-primary">
              <div className="card-body text-center">
                <h5>Budget Annuel FIRE</h5>
                <h4>{formatCurrency(summary.total_budget_annuel)}</h4>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card text-white bg-info">
              <div className="card-body text-center">
                <h5>Dépenses Réalisées</h5>
                <h4>{formatCurrency(summary.total_depenses_annee)}</h4>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className={`card text-white ${summary.total_ecart_euros >= 0 ? 'bg-danger' : 'bg-success'}`}>
              <div className="card-body text-center">
                <h5>Écart Budget</h5>
                <h4>{formatCurrency(Math.abs(summary.total_ecart_euros))}</h4>
                <small>{summary.total_ecart_euros >= 0 ? 'Dépassement' : 'Économie'}</small>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card text-white bg-warning">
              <div className="card-body text-center">
                <h5>Retrait Brut Nécessaire</h5>
                <h4>{formatCurrency(summary.retrait_brut_necessaire)}</h4>
                <small>+30% impôts/PS</small>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Impact sur objectif FIRE */}
      {summary && (
        <Alert variant={summary.total_ecart_euros >= 0 ? 'warning' : 'success'} className="mb-4">
          <Alert.Heading>Impact sur l'objectif FIRE 2038</Alert.Heading>
          <p className="mb-0">{summary.impact_date_fire}</p>
        </Alert>
      )}

      {/* Tableau détaillé par catégorie */}
      <div className="table-responsive">
        <table className="table table-striped table-hover">
          <thead className="table-dark">
            <tr>
              <th>Catégorie</th>
              <th className="text-end">Budget Annuel</th>
              <th className="text-end">Réalisé Année</th>
              <th className="text-end">Réalisé Mois</th>
              <th className="text-end">Écart €</th>
              <th className="text-end">Écart %</th>
              <th className="text-end">Projection</th>
              <th className="text-center">Statut</th>
              <th className="text-center">Progression</th>
            </tr>
          </thead>
          <tbody>
            {analyses.map(analysis => (
              <tr key={analysis.categorie_id}>
                <td><strong>{analysis.nom_categorie}</strong></td>
                <td className="text-end">{formatCurrency(analysis.budget_annuel)}</td>
                <td className="text-end">{formatCurrency(analysis.depenses_annee_actuelle)}</td>
                <td className="text-end">{formatCurrency(analysis.depenses_mois_actuel)}</td>
                <td className={`text-end ${analysis.ecart_euros >= 0 ? 'text-danger' : 'text-success'}`}>
                  {formatCurrency(analysis.ecart_euros)}
                </td>
                <td className={`text-end ${analysis.ecart_pourcentage >= 0 ? 'text-danger' : 'text-success'}`}>
                  {formatPercentage(analysis.ecart_pourcentage)}
                </td>
                <td className="text-end">{formatCurrency(analysis.projection_annuelle)}</td>
                <td className="text-center">
                  <span className={`badge bg-${getStatusColor(analysis.statut)}`}>
                    {getStatusText(analysis.statut)}
                  </span>
                </td>
                <td className="text-center" style={{ width: '120px' }}>
                  <ProgressBar
                    now={Math.min((analysis.depenses_annee_actuelle / analysis.budget_annuel) * 100, 100)}
                    variant={getStatusColor(analysis.statut)}
                    style={{ height: '8px' }}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal pour ajouter/modifier une dépense */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedDepense?.id ? 'Modifier la dépense' : 'Ajouter une dépense'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <DepenseForm 
            depense={selectedDepense}
            categories={categories}
            onSave={handleSave} 
            onCancel={() => setShowModal(false)} 
          />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default BudgetFire;
