import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { Modal, Button, Form, Alert, ProgressBar } from 'react-bootstrap';

interface BudgetCategory {
  id: number;
  nom: string;
  budget_annuel: number;
  description: string;
  ordre_affichage: number;
}

interface BudgetSummary {
  total_budget_annuel: number;
  retrait_brut_necessaire: number;
  impact_date_fire: string;
}

const BudgetCategoryForm: React.FC<{
  category: Partial<BudgetCategory> | null,
  onSave: () => void,
  onCancel: () => void
}> = ({ category, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    nom: category?.nom || '',
    budget_annuel: category?.budget_annuel || 0,
    description: category?.description || '',
    ordre_affichage: category?.ordre_affichage || 0,
  });

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    const method = category?.id ? 'put' : 'post';
    const url = category?.id ? `/budget/categories/${category.id}` : '/budget/categories';

    console.log('Submitting budget category:', { method, url, formData });

    try {
      const response = await apiClient[method](url, formData);
      console.log('Budget category saved successfully:', response.data);
      onSave();
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde de la catégorie", error);
      console.error("Error details:", error.response?.data);
      alert(`Erreur: ${error.response?.data?.detail || error.message}`);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Nom de la catégorie</Form.Label>
        <Form.Control
          type="text"
          value={formData.nom}
          onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
          required
          placeholder="Ex: Alimentation, Assurance Maison..."
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Budget annuel (€)</Form.Label>
        <Form.Control
          type="number"
          step="0.01"
          value={formData.budget_annuel}
          onChange={(e) => setFormData({ ...formData, budget_annuel: parseFloat(e.target.value) || 0 })}
          required
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Description</Form.Label>
        <Form.Control
          type="text"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Description de la catégorie..."
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Ordre d'affichage</Form.Label>
        <Form.Control
          type="number"
          value={formData.ordre_affichage}
          onChange={(e) => setFormData({ ...formData, ordre_affichage: parseInt(e.target.value) || 0 })}
          placeholder="Ordre d'affichage (1, 2, 3...)"
        />
        <Form.Text className="text-muted">
          Plus le nombre est petit, plus la catégorie apparaîtra en haut de la liste
        </Form.Text>
      </Form.Group>

      <div className="d-flex justify-content-end gap-2">
        <Button variant="secondary" onClick={onCancel}>Annuler</Button>
        <Button variant="primary" type="submit">Sauvegarder</Button>
      </div>
    </Form>
  );
};



const BudgetFire: React.FC = () => {
  const [categories, setCategories] = useState<BudgetCategory[]>([]);
  const [summary, setSummary] = useState<BudgetSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Partial<BudgetCategory> | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [showCategoriesTable, setShowCategoriesTable] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [categoriesRes, summaryRes] = await Promise.all([
        apiClient.get('/budget/categories'),
        apiClient.get('/budget/summary')
      ]);

      setCategories(categoriesRes.data);
      setSummary(summaryRes.data);
      setLoading(false);
      setRetryCount(0);
    } catch (error: any) {
      console.error('Budget API error:', error);

      if (retryCount < 2) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchData(), 1000);
      } else {
        setError('Erreur lors de la récupération des données budgétaires.');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleCategorySave = () => {
    setShowCategoryModal(false);
    setSelectedCategory(null);
    fetchData();
  };

  const handleDeleteCategory = async (categoryId: number, categoryName: string) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${categoryName}" ? Cette action est irréversible.`)) {
      try {
        await apiClient.delete(`/budget/categories/${categoryId}`);
        fetchData();
      } catch (error: any) {
        console.error("Erreur lors de la suppression de la catégorie", error);
        alert(`Erreur lors de la suppression: ${error.response?.data?.detail || error.message}`);
      }
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  };

  if (loading && !showCategoryModal) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchData}>
        Réessayer
      </button>
    </div>
  );

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Calculateur Budget FIRE</h1>
        <div className="d-flex gap-2">
          <Button
            variant="outline-secondary"
            onClick={() => setShowCategoriesTable(!showCategoriesTable)}
          >
            {showCategoriesTable ? 'Masquer' : 'Gérer'} Catégories
          </Button>
          <Button
            variant="success"
            onClick={() => { setSelectedCategory({}); setShowCategoryModal(true); }}
          >
            Nouvelle Catégorie
          </Button>
        </div>
      </div>

      {/* Résumé FIRE */}
      {summary && (
        <div className="row mb-4">
          <div className="col-md-4">
            <div className="card text-white bg-primary">
              <div className="card-body text-center">
                <h5>Budget Annuel FIRE</h5>
                <h4>{formatCurrency(summary.total_budget_annuel)}</h4>
                <small>Dépenses nettes cibles</small>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card text-white bg-warning">
              <div className="card-body text-center">
                <h5>Retrait Brut Nécessaire</h5>
                <h4>{formatCurrency(summary.retrait_brut_necessaire)}</h4>
                <small>+30% impôts/PS</small>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card text-white bg-success">
              <div className="card-body text-center">
                <h5>Capital FIRE Requis</h5>
                <h4>{formatCurrency(910150)}</h4>
                <small>Objectif documenté</small>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Information FIRE */}
      {summary && (
        <Alert variant="info" className="mb-4">
          <Alert.Heading>Objectif FIRE 2038</Alert.Heading>
          <p className="mb-0">
            Avec ce budget de <strong>{formatCurrency(summary.total_budget_annuel)}</strong> par an,
            votre objectif FIRE documenté est de <strong>{formatCurrency(910150)}</strong>
            (retrait brut de <strong>{formatCurrency(summary.retrait_brut_necessaire)}</strong>/an).
          </p>
        </Alert>
      )}

      {/* Tableau de gestion des catégories */}
      {showCategoriesTable && (
        <div className="mb-4">
          <h3>Gestion des Catégories Budgétaires</h3>
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead className="table-secondary">
                <tr>
                  <th>Ordre</th>
                  <th>Nom</th>
                  <th className="text-end">Budget Annuel</th>
                  <th>Description</th>
                  <th className="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {categories.map(category => (
                  <tr key={category.id}>
                    <td>{category.ordre_affichage}</td>
                    <td><strong>{category.nom}</strong></td>
                    <td className="text-end">{formatCurrency(category.budget_annuel)}</td>
                    <td>{category.description}</td>
                    <td className="text-center">
                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="me-2"
                        onClick={() => { setSelectedCategory(category); setShowCategoryModal(true); }}
                      >
                        Modifier
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleDeleteCategory(category.id, category.nom)}
                      >
                        Supprimer
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="table-info">
                  <th colSpan={2}>TOTAL</th>
                  <th className="text-end">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0))}</th>
                  <th colSpan={2}></th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {/* Tableau des catégories budgétaires FIRE */}
      <div className="table-responsive">
        <table className="table table-striped table-hover">
          <thead className="table-dark">
            <tr>
              <th>Catégorie</th>
              <th className="text-end">Budget Annuel</th>
              <th className="text-end">Budget Mensuel</th>
            </tr>
          </thead>
          <tbody>
            {categories.map(category => (
              <tr key={category.id}>
                <td>
                  <strong>{category.nom}</strong>
                  {category.description && (
                    <>
                      <br />
                      <small className="text-muted">{category.description}</small>
                    </>
                  )}
                </td>
                <td className="text-end">{formatCurrency(category.budget_annuel)}</td>
                <td className="text-end">{formatCurrency(category.budget_annuel / 12)}</td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="table-info">
              <th>TOTAL</th>
              <th className="text-end">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0))}</th>
              <th className="text-end">{formatCurrency(categories.reduce((sum, cat) => sum + cat.budget_annuel, 0) / 12)}</th>
            </tr>
          </tfoot>
        </table>
      </div>

      {/* Modal pour ajouter/modifier une catégorie budgétaire */}
      <Modal show={showCategoryModal} onHide={() => setShowCategoryModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedCategory?.id ? `Modifier "${selectedCategory.nom}"` : 'Nouvelle catégorie budgétaire'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <BudgetCategoryForm
            category={selectedCategory}
            onSave={handleCategorySave}
            onCancel={() => setShowCategoryModal(false)}
          />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default BudgetFire;
