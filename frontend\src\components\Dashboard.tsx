import React, { useEffect, useState } from 'react';
import apiClient from './ApiClient';
import { Doughn<PERSON> } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { ProgressBar } from 'react-bootstrap';

ChartJS.register(Arc<PERSON><PERSON>, Tooltip, Legend);

interface DashboardData {
  net_patrimoine: number;
  total_assets: number;
  total_liabilities: number;
  allocation: { [key: string]: number };
}

interface AllocationTarget {
  category: string;
  categoryKey: string;
  currentValue: number;
  currentPercent: number;
  targetPercent: number;
  targetValue: number;
  amountToInvest: number;
  progressPercent: number;
}

const Dashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const fetchDashboardData = () => {
    setLoading(true);
    setError(null);

    apiClient.get('/dashboard')
      .then(response => {
        setData(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('Dashboard API error:', error);

        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchDashboardData(), 1000);
        } else {
          setError('Erreur lors de la récupération des données du dashboard.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Configuration de l'allocation cible FIRE basée sur Patrimoine.md
  const calculateTargetAllocation = (): AllocationTarget[] => {
    if (!data) return [];

    const FIRE_TARGET = 910150; // Objectif FIRE documenté

    // Mapping des catégories avec leurs pourcentages cibles
    const targetAllocations = [
      { category: "Liquidité", categoryKey: "Liquidité", targetPercent: 2.5 },
      { category: "Bourse", categoryKey: "Bourse", targetPercent: 35.5 },
      { category: "Immobilier", categoryKey: "Immobilier", targetPercent: 35.0 },
      { category: "Fonds sécurisés", categoryKey: "Fonds sécurisés", targetPercent: 20.0 },
      { category: "Prêts participatifs", categoryKey: "Prêts participatifs", targetPercent: 2.0 },
      { category: "Crypto-Actifs", categoryKey: "Crypto-Actifs", targetPercent: 5.0 }
    ];

    return targetAllocations.map(target => {
      const currentValue = data.allocation[target.categoryKey] || 0;
      const currentPercent = data.total_assets > 0 ? (currentValue / data.total_assets) * 100 : 0;
      const targetValue = (FIRE_TARGET * target.targetPercent) / 100;
      const amountToInvest = Math.max(0, targetValue - currentValue);
      const progressPercent = targetValue > 0 ? Math.min(100, (currentValue / targetValue) * 100) : 0;

      return {
        category: target.category,
        categoryKey: target.categoryKey,
        currentValue,
        currentPercent,
        targetPercent: target.targetPercent,
        targetValue,
        amountToInvest,
        progressPercent
      };
    });
  };

  const allocationTargets = calculateTargetAllocation();

  if (loading) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchDashboardData}>
        Réessayer
      </button>
    </div>
  );
  if (!data) return <p>Aucune donnée disponible.</p>;

  const chartData = {
    labels: Object.keys(data.allocation),
    datasets: [
      {
        label: 'Répartition du portefeuille',
        data: Object.values(data.allocation),
        backgroundColor: [
          'rgba(255, 99, 132, 0.7)',
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)',
          'rgba(255, 159, 64, 0.7)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div>
      <h1 className="mb-4">Dashboard</h1>
      <div className="row">
        <div className="col-md-4">
          <div className="card text-white bg-primary mb-3">
            <div className="card-header">Patrimoine Net Total</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.net_patrimoine)}</h5>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-success mb-3">
            <div className="card-header">Total Actifs</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_assets)}</h5>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-danger mb-3">
            <div className="card-header">Total Passifs</div>
            <div className="card-body">
              <h5 className="card-title">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_liabilities)}</h5>
            </div>
          </div>
        </div>
      </div>
      <div className="row mt-4">
        <div className="col-md-6">
          <h2>Répartition des Actifs</h2>
          <Doughnut data={chartData} />
        </div>
      </div>

      {/* Section Allocation Cible FIRE */}
      <div className="row mt-5">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h3 className="mb-0">🎯 Allocation Cible FIRE</h3>
              <small className="text-muted">
                Progression vers l'objectif de 910 150€ selon la stratégie documentée
              </small>
            </div>
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-striped table-hover">
                  <thead className="table-dark">
                    <tr>
                      <th>Catégorie d'Actif</th>
                      <th className="text-end">Valeur Actuelle</th>
                      <th className="text-end">% Actuel</th>
                      <th className="text-end">% Cible</th>
                      <th className="text-end">Valeur Cible</th>
                      <th className="text-end">Montant à Investir</th>
                      <th className="text-center">Progression</th>
                    </tr>
                  </thead>
                  <tbody>
                    {allocationTargets.map((target, index) => (
                      <tr key={index}>
                        <td><strong>{target.category}</strong></td>
                        <td className="text-end">
                          {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(target.currentValue)}
                        </td>
                        <td className="text-end">
                          {target.currentPercent.toFixed(1)}%
                        </td>
                        <td className="text-end">
                          <strong>{target.targetPercent}%</strong>
                        </td>
                        <td className="text-end">
                          {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(target.targetValue)}
                        </td>
                        <td className="text-end">
                          {target.amountToInvest > 0 ? (
                            <span className="text-warning">
                              {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(target.amountToInvest)}
                            </span>
                          ) : (
                            <span className="text-success">Objectif atteint</span>
                          )}
                        </td>
                        <td className="text-center" style={{ width: '150px' }}>
                          <ProgressBar
                            now={target.progressPercent}
                            variant={target.progressPercent >= 100 ? 'success' : target.progressPercent >= 75 ? 'info' : target.progressPercent >= 50 ? 'warning' : 'danger'}
                            style={{ height: '20px' }}
                            label={`${target.progressPercent.toFixed(0)}%`}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="table-info">
                      <th>TOTAL</th>
                      <th className="text-end">
                        {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(data.total_assets)}
                      </th>
                      <th className="text-end">100%</th>
                      <th className="text-end">100%</th>
                      <th className="text-end">
                        {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(910150)}
                      </th>
                      <th className="text-end">
                        <strong className="text-primary">
                          {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(Math.max(0, 910150 - data.total_assets))}
                        </strong>
                      </th>
                      <th className="text-center">
                        <strong>
                          {((data.total_assets / 910150) * 100).toFixed(1)}%
                        </strong>
                      </th>
                    </tr>
                  </tfoot>
                </table>
              </div>

              {/* Résumé et recommandations */}
              <div className="row mt-4">
                <div className="col-md-4">
                  <div className="card text-white bg-info">
                    <div className="card-body text-center">
                      <h5>Progression Globale</h5>
                      <h4>{((data.total_assets / 910150) * 100).toFixed(1)}%</h4>
                      <small>vers l'objectif FIRE</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card text-white bg-warning">
                    <div className="card-body text-center">
                      <h5>Capital Restant</h5>
                      <h4>{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(Math.max(0, 910150 - data.total_assets))}</h4>
                      <small>à investir</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card text-white bg-success">
                    <div className="card-body text-center">
                      <h5>Estimation</h5>
                      <h4>{Math.max(0, Math.ceil((910150 - data.total_assets) / (3415 * 12)))} ans</h4>
                      <small>à 3 415€/mois</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;