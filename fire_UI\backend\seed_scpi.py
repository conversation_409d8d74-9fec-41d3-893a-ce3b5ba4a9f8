import datetime
from sqlalchemy.orm import Session
from database import SessionLocal
import models

def seed_scpi_data():
    db = SessionLocal()
    
    # Clear existing SCPI data
    db.query(models.SCPI).delete()
    db.commit()

    # SCPI data from Patrimoine.md
    scpi_data = [
        {
            "name": "EPARGNE FONCIERE",
            "price_per_share": 619.75,
            "number_of_shares": 38,
            "total_value": 23550.5,
            "update_date": datetime.date(2025, 7, 1)
        },
        {
            "name": "PFO2",
            "price_per_share": 150.06,
            "number_of_shares": 150,
            "total_value": 22509,
            "update_date": datetime.date(2025, 7, 1)
        },
        {
            "name": "AESTIAM PIERRE RENDEMENT",
            "price_per_share": 829.8,
            "number_of_shares": 30,
            "total_value": 24894,
            "update_date": datetime.date(2025, 7, 1)
        },
        {
            "name": "Moniwan : LF OPPORTUNITE IMMO",
            "price_per_share": 184.73,
            "number_of_shares": 57,
            "total_value": 10529.61,
            "update_date": datetime.date(2025, 7, 1)
        }
    ]

    # Create SCPI records
    for scpi_info in scpi_data:
        scpi = models.SCPI(**scpi_info)
        db.add(scpi)
    
    db.commit()
    db.close()
    
    print(f"Added {len(scpi_data)} SCPI records to the database")
    print(f"Total SCPI value: {sum(scpi['total_value'] for scpi in scpi_data):,.2f} €")

if __name__ == "__main__":
    seed_scpi_data()
