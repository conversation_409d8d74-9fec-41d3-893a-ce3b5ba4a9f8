{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\FireTarget.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Button, Form } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FireSettingsForm = ({\n  settings,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    fire_target_amount: (settings === null || settings === void 0 ? void 0 : settings.fire_target_amount) || 910150,\n    secure_withdrawal_rate: ((settings === null || settings === void 0 ? void 0 : settings.secure_withdrawal_rate) || 0.04) * 100 // Convert to percentage for display\n  });\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const dataToSend = {\n      fire_target_amount: formData.fire_target_amount,\n      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100 // Convert back to decimal\n    };\n    console.log('Updating FIRE settings:', dataToSend);\n    try {\n      const response = await apiClient.put('/fire-settings', dataToSend);\n      console.log('FIRE settings updated successfully:', response.data);\n      onSave();\n    } catch (error) {\n      var _error$response;\n      console.error(\"Erreur lors de la sauvegarde des paramètres FIRE\", error);\n      console.error(\"Error details:\", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Objectif FIRE (\\u20AC)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"1000\",\n        value: formData.fire_target_amount,\n        onChange: e => setFormData({\n          ...formData,\n          fire_target_amount: parseFloat(e.target.value) || 0\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n        className: \"text-muted\",\n        children: \"Montant total que vous souhaitez atteindre pour votre ind\\xE9pendance financi\\xE8re\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Taux de retrait s\\xE9curis\\xE9 (%)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        step: \"0.1\",\n        min: \"1\",\n        max: \"10\",\n        value: formData.secure_withdrawal_rate,\n        onChange: e => setFormData({\n          ...formData,\n          secure_withdrawal_rate: parseFloat(e.target.value) || 4\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n        className: \"text-muted\",\n        children: \"Pourcentage de votre patrimoine que vous pouvez retirer annuellement (r\\xE8gle des 4% = 4.0)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-end gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onCancel,\n        children: \"Annuler\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        type: \"submit\",\n        children: \"Sauvegarder\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(FireSettingsForm, \"NWi8mFpjauSzY6iBewN5nVDd2pQ=\");\n_c = FireSettingsForm;\nconst FireTarget = () => {\n  _s2();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const [showModal, setShowModal] = useState(false);\n  const fetchFireTargetData = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/fire-target').then(response => {\n      setData(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('FireTarget API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchFireTargetData(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des données FIRE.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchFireTargetData();\n  }, []);\n  const handleSettingsSave = () => {\n    setShowModal(false);\n    fetchFireTargetData(); // Refresh data after settings update\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchFireTargetData,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 21\n  }, this);\n  const formatCurrency = value => new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(value);\n  const formatPercentage = value => new Intl.NumberFormat('fr-FR', {\n    style: 'percent',\n    minimumFractionDigits: 1\n  }).format(value / 100);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"mb-4\",\n      children: \"Objectif FIRE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: \"Progression vers votre Objectif\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Vous avez atteint \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formatCurrency(data.current_net_patrimoine)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 35\n              }, this), \" sur votre objectif de \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formatCurrency(data.fire_target_amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 120\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress\",\n              style: {\n                height: '25px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-bar progress-bar-striped progress-bar-animated\",\n                role: \"progressbar\",\n                style: {\n                  width: `${progressPercentage}%`\n                },\n                \"aria-valuenow\": progressPercentage,\n                \"aria-valuemin\": 0,\n                \"aria-valuemax\": 100,\n                children: [progressPercentage.toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Montant restant \\xE0 investir :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), \" \", formatCurrency(data.remaining_to_invest)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"Revenu passif annuel potentiel (SWR \", data.secure_withdrawal_rate * 100, \"%) :\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), \" \", formatCurrency(data.potential_passive_income)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s2(FireTarget, \"guE780lgw85iT/qxPW4RqWbWqkY=\");\n_c2 = FireTarget;\nexport default FireTarget;\nvar _c, _c2;\n$RefreshReg$(_c, \"FireSettingsForm\");\n$RefreshReg$(_c2, \"FireTarget\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "<PERSON><PERSON>", "Form", "jsxDEV", "_jsxDEV", "FireSettingsForm", "settings", "onSave", "onCancel", "_s", "formData", "setFormData", "fire_target_amount", "secure_withdrawal_rate", "handleSubmit", "e", "preventDefault", "dataToSend", "console", "log", "response", "put", "data", "error", "_error$response", "onSubmit", "children", "Group", "className", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "type", "step", "value", "onChange", "parseFloat", "target", "required", "Text", "min", "max", "variant", "onClick", "_c", "FireTarget", "_s2", "setData", "loading", "setLoading", "setError", "retryCount", "setRetryCount", "showModal", "setShowModal", "fetchFireTargetData", "get", "then", "catch", "prev", "setTimeout", "handleSettingsSave", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "formatPercentage", "minimumFractionDigits", "current_net_patrimoine", "height", "role", "width", "progressPercentage", "toFixed", "remaining_to_invest", "potential_passive_income", "_c2", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/FireTarget.tsx"], "sourcesContent": ["import React, { useEffect, useState, FormEvent } from 'react';\nimport apiClient from './ApiClient';\nimport { <PERSON><PERSON>, Button, Form, ProgressBar } from 'react-bootstrap';\n\ninterface FireTargetData {\n  fire_target_amount: number;\n  secure_withdrawal_rate: number;\n  current_net_patrimoine: number;\n  total_assets: number;\n  total_liabilities: number;\n  remaining_to_invest: number;\n  potential_passive_income: number;\n  progress_percentage: number;\n}\n\ninterface FireSettings {\n  id: number;\n  fire_target_amount: number;\n  secure_withdrawal_rate: number;\n  update_date: string;\n}\n\nconst FireSettingsForm: React.FC<{\n  settings: { fire_target_amount: number; secure_withdrawal_rate: number } | null,\n  onSave: () => void,\n  onCancel: () => void\n}> = ({ settings, onSave, onCancel }) => {\n  const [formData, setFormData] = useState({\n    fire_target_amount: settings?.fire_target_amount || 910150,\n    secure_withdrawal_rate: (settings?.secure_withdrawal_rate || 0.04) * 100, // Convert to percentage for display\n  });\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n\n    const dataToSend = {\n      fire_target_amount: formData.fire_target_amount,\n      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100, // Convert back to decimal\n    };\n\n    console.log('Updating FIRE settings:', dataToSend);\n\n    try {\n      const response = await apiClient.put('/fire-settings', dataToSend);\n      console.log('FIRE settings updated successfully:', response.data);\n      onSave();\n    } catch (error: any) {\n      console.error(\"Erreur lors de la sauvegarde des paramètres FIRE\", error);\n      console.error(\"Error details:\", error.response?.data);\n    }\n  };\n\n  return (\n    <Form onSubmit={handleSubmit}>\n      <Form.Group className=\"mb-3\">\n        <Form.Label>Objectif FIRE (€)</Form.Label>\n        <Form.Control\n          type=\"number\"\n          step=\"1000\"\n          value={formData.fire_target_amount}\n          onChange={(e) => setFormData({ ...formData, fire_target_amount: parseFloat(e.target.value) || 0 })}\n          required\n        />\n        <Form.Text className=\"text-muted\">\n          Montant total que vous souhaitez atteindre pour votre indépendance financière\n        </Form.Text>\n      </Form.Group>\n\n      <Form.Group className=\"mb-3\">\n        <Form.Label>Taux de retrait sécurisé (%)</Form.Label>\n        <Form.Control\n          type=\"number\"\n          step=\"0.1\"\n          min=\"1\"\n          max=\"10\"\n          value={formData.secure_withdrawal_rate}\n          onChange={(e) => setFormData({ ...formData, secure_withdrawal_rate: parseFloat(e.target.value) || 4 })}\n          required\n        />\n        <Form.Text className=\"text-muted\">\n          Pourcentage de votre patrimoine que vous pouvez retirer annuellement (règle des 4% = 4.0)\n        </Form.Text>\n      </Form.Group>\n\n      <div className=\"d-flex justify-content-end gap-2\">\n        <Button variant=\"secondary\" onClick={onCancel}>Annuler</Button>\n        <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\n      </div>\n    </Form>\n  );\n};\n\nconst FireTarget: React.FC = () => {\n  const [data, setData] = useState<FireTargetData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const [showModal, setShowModal] = useState(false);\n\n  const fetchFireTargetData = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/fire-target')\n      .then(response => {\n        setData(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('FireTarget API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchFireTargetData(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des données FIRE.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchFireTargetData();\n  }, []);\n\n  const handleSettingsSave = () => {\n    setShowModal(false);\n    fetchFireTargetData(); // Refresh data after settings update\n  };\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchFireTargetData}>\n        Réessayer\n      </button>\n    </div>\n  );\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);\n  const formatPercentage = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 1 }).format(value / 100);\n\n  return (\n    <div>\n      <h1 className=\"mb-4\">Objectif FIRE</h1>\n      <div className=\"row\">\n        <div className=\"col-md-8\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">Progression vers votre Objectif</h5>\n              <p>\n                Vous avez atteint <strong>{formatCurrency(data.current_net_patrimoine)}</strong> sur votre objectif de <strong>{formatCurrency(data.fire_target_amount)}</strong>.\n              </p>\n              <div className=\"progress\" style={{ height: '25px' }}>\n                <div\n                  className=\"progress-bar progress-bar-striped progress-bar-animated\"\n                  role=\"progressbar\"\n                  style={{ width: `${progressPercentage}%` }}\n                  aria-valuenow={progressPercentage}\n                  aria-valuemin={0}\n                  aria-valuemax={100}\n                >\n                  {progressPercentage.toFixed(2)}%\n                </div>\n              </div>\n              <hr />\n              <p>\n                <strong>Montant restant à investir :</strong> {formatCurrency(data.remaining_to_invest)}\n              </p>\n              <p>\n                <strong>Revenu passif annuel potentiel (SWR {data.secure_withdrawal_rate * 100}%) :</strong> {formatCurrency(data.potential_passive_income)}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FireTarget;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAC7D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAAgBC,MAAM,EAAEC,IAAI,QAAqB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBnE,MAAMC,gBAIJ,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACvCa,kBAAkB,EAAE,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,kBAAkB,KAAI,MAAM;IAC1DC,sBAAsB,EAAE,CAAC,CAAAP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,sBAAsB,KAAI,IAAI,IAAI,GAAG,CAAE;EAC5E,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,MAAOC,CAAY,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,UAAU,GAAG;MACjBL,kBAAkB,EAAEF,QAAQ,CAACE,kBAAkB;MAC/CC,sBAAsB,EAAEH,QAAQ,CAACG,sBAAsB,GAAG,GAAG,CAAE;IACjE,CAAC;IAEDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,UAAU,CAAC;IAElD,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMpB,SAAS,CAACqB,GAAG,CAAC,gBAAgB,EAAEJ,UAAU,CAAC;MAClEC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACjEf,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOgB,KAAU,EAAE;MAAA,IAAAC,eAAA;MACnBN,OAAO,CAACK,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxEL,OAAO,CAACK,KAAK,CAAC,gBAAgB,GAAAC,eAAA,GAAED,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,CAAC;IACvD;EACF,CAAC;EAED,oBACElB,OAAA,CAACF,IAAI;IAACuB,QAAQ,EAAEX,YAAa;IAAAY,QAAA,gBAC3BtB,OAAA,CAACF,IAAI,CAACyB,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBAC1BtB,OAAA,CAACF,IAAI,CAAC2B,KAAK;QAAAH,QAAA,EAAC;MAAiB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC1C7B,OAAA,CAACF,IAAI,CAACgC,OAAO;QACXC,IAAI,EAAC,QAAQ;QACbC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAE3B,QAAQ,CAACE,kBAAmB;QACnC0B,QAAQ,EAAGvB,CAAC,IAAKJ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEE,kBAAkB,EAAE2B,UAAU,CAACxB,CAAC,CAACyB,MAAM,CAACH,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QACnGI,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF7B,OAAA,CAACF,IAAI,CAACwC,IAAI;QAACd,SAAS,EAAC,YAAY;QAAAF,QAAA,EAAC;MAElC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEb7B,OAAA,CAACF,IAAI,CAACyB,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBAC1BtB,OAAA,CAACF,IAAI,CAAC2B,KAAK;QAAAH,QAAA,EAAC;MAA4B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACrD7B,OAAA,CAACF,IAAI,CAACgC,OAAO;QACXC,IAAI,EAAC,QAAQ;QACbC,IAAI,EAAC,KAAK;QACVO,GAAG,EAAC,GAAG;QACPC,GAAG,EAAC,IAAI;QACRP,KAAK,EAAE3B,QAAQ,CAACG,sBAAuB;QACvCyB,QAAQ,EAAGvB,CAAC,IAAKJ,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEG,sBAAsB,EAAE0B,UAAU,CAACxB,CAAC,CAACyB,MAAM,CAACH,KAAK,CAAC,IAAI;QAAE,CAAC,CAAE;QACvGI,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF7B,OAAA,CAACF,IAAI,CAACwC,IAAI;QAACd,SAAS,EAAC,YAAY;QAAAF,QAAA,EAAC;MAElC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEb7B,OAAA;MAAKwB,SAAS,EAAC,kCAAkC;MAAAF,QAAA,gBAC/CtB,OAAA,CAACH,MAAM;QAAC4C,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEtC,QAAS;QAAAkB,QAAA,EAAC;MAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/D7B,OAAA,CAACH,MAAM;QAAC4C,OAAO,EAAC,SAAS;QAACV,IAAI,EAAC,QAAQ;QAAAT,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACxB,EAAA,CApEIJ,gBAIJ;AAAA0C,EAAA,GAJI1C,gBAIJ;AAkEF,MAAM2C,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM,CAAC3B,IAAI,EAAE4B,OAAO,CAAC,GAAGnD,QAAQ,CAAwB,IAAI,CAAC;EAC7D,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAE8B,QAAQ,CAAC,GAAGtD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM2D,mBAAmB,GAAGA,CAAA,KAAM;IAChCN,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEdrD,SAAS,CAAC2D,GAAG,CAAC,cAAc,CAAC,CAC1BC,IAAI,CAACxC,QAAQ,IAAI;MAChB8B,OAAO,CAAC9B,QAAQ,CAACE,IAAI,CAAC;MACtB8B,UAAU,CAAC,KAAK,CAAC;MACjBG,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDM,KAAK,CAACtC,KAAK,IAAI;MACdL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAE7C,IAAI+B,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAML,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAC,MAAM;QACLL,QAAQ,CAAC,kDAAkD,CAAC;QAC5DD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDtD,SAAS,CAAC,MAAM;IACd4D,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/BP,YAAY,CAAC,KAAK,CAAC;IACnBC,mBAAmB,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,IAAIP,OAAO,EAAE,oBAAO/C,OAAA;IAAAsB,QAAA,EAAG;EAAa;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIV,KAAK,EAAE,oBACTnB,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAGwB,SAAS,EAAC,aAAa;MAAAF,QAAA,EAAEH;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtC7B,OAAA;MAAQwB,SAAS,EAAC,iBAAiB;MAACkB,OAAO,EAAEY,mBAAoB;MAAAhC,QAAA,EAAC;IAElE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAER,IAAI,CAACX,IAAI,EAAE,oBAAOlB,OAAA;IAAAsB,QAAA,EAAG;EAAyB;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAMgC,cAAc,GAAI5B,KAAa,IAAK,IAAI6B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAAEC,KAAK,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC;EAC9H,MAAMkC,gBAAgB,GAAIlC,KAAa,IAAK,IAAI6B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAAEC,KAAK,EAAE,SAAS;IAAEI,qBAAqB,EAAE;EAAE,CAAC,CAAC,CAACF,MAAM,CAACjC,KAAK,GAAG,GAAG,CAAC;EAE9I,oBACEjC,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAIwB,SAAS,EAAC,MAAM;MAAAF,QAAA,EAAC;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvC7B,OAAA;MAAKwB,SAAS,EAAC,KAAK;MAAAF,QAAA,eAClBtB,OAAA;QAAKwB,SAAS,EAAC,UAAU;QAAAF,QAAA,eACvBtB,OAAA;UAAKwB,SAAS,EAAC,MAAM;UAAAF,QAAA,eACnBtB,OAAA;YAAKwB,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBtB,OAAA;cAAIwB,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAC;YAA+B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D7B,OAAA;cAAAsB,QAAA,GAAG,oBACiB,eAAAtB,OAAA;gBAAAsB,QAAA,EAASuC,cAAc,CAAC3C,IAAI,CAACmD,sBAAsB;cAAC;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,2BAAuB,eAAA7B,OAAA;gBAAAsB,QAAA,EAASuC,cAAc,CAAC3C,IAAI,CAACV,kBAAkB;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACnK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7B,OAAA;cAAKwB,SAAS,EAAC,UAAU;cAACwC,KAAK,EAAE;gBAAEM,MAAM,EAAE;cAAO,CAAE;cAAAhD,QAAA,eAClDtB,OAAA;gBACEwB,SAAS,EAAC,yDAAyD;gBACnE+C,IAAI,EAAC,aAAa;gBAClBP,KAAK,EAAE;kBAAEQ,KAAK,EAAE,GAAGC,kBAAkB;gBAAI,CAAE;gBAC3C,iBAAeA,kBAAmB;gBAClC,iBAAe,CAAE;gBACjB,iBAAe,GAAI;gBAAAnD,QAAA,GAElBmD,kBAAkB,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7B,OAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7B,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAAsB,QAAA,EAAQ;cAA4B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACgC,cAAc,CAAC3C,IAAI,CAACyD,mBAAmB,CAAC;YAAA;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACJ7B,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAAsB,QAAA,GAAQ,sCAAoC,EAACJ,IAAI,CAACT,sBAAsB,GAAG,GAAG,EAAC,MAAI;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACgC,cAAc,CAAC3C,IAAI,CAAC0D,wBAAwB,CAAC;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACgB,GAAA,CAzFID,UAAoB;AAAAiC,GAAA,GAApBjC,UAAoB;AA2F1B,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAAkC,GAAA;AAAAC,YAAA,CAAAnC,EAAA;AAAAmC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}