import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Form, Table, Badge, Alert } from 'react-bootstrap';
import apiClient from './ApiClient';

interface ScenarioResult {
  swr: number;
  returnRate: number;
  requiredCapital: number;
  yearsToFire: number;
  fireDate: number;
  ageAtFire: number;
  monthlyInvestmentNeeded: number;
  status: 'early' | 'ontime' | 'delayed';
}

interface FireData {
  current_net_patrimoine: number;
  fire_target_amount: number;
}

const ScenariosFire: React.FC = () => {
  const [fireData, setFireData] = useState<FireData | null>(null);
  const [customSwr, setCustomSwr] = useState(4.0);
  const [customReturn, setCustomReturn] = useState(5.0);
  const [loading, setLoading] = useState(true);

  // Constantes basées sur la documentation FIRE
  const CURRENT_YEAR = 2025;
  const BIRTH_YEAR = 1978;
  const TARGET_YEAR = 2038;
  const ANNUAL_NET_EXPENSES = 25484; // Budget net documenté
  const ANNUAL_GROSS_WITHDRAWAL = 36406; // Retrait brut documenté
  const MONTHLY_INVESTMENT_CAPACITY = 3415; // Capacité d'investissement mensuelle documentée

  // Scénarios prédéfinis
  const swrScenarios = [
    { name: 'Conservateur', rate: 3.8, description: 'Sécurité maximale' },
    { name: 'Standard', rate: 4.0, description: 'Règle classique' },
    { name: 'Agressif', rate: 4.5, description: 'Optimiste' }
  ];

  const returnScenarios = [
    { name: 'Conservateur', rate: 4.0, description: 'Marché difficile' },
    { name: 'Modéré', rate: 5.0, description: 'Hypothèse actuelle' },
    { name: 'Optimiste', rate: 6.0, description: 'Marché favorable' }
  ];

  useEffect(() => {
    fetchFireData();
  }, []);

  const fetchFireData = async () => {
    try {
      const response = await apiClient.get('/fire-target');
      setFireData(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching FIRE data:', error);
      setLoading(false);
    }
  };

  const calculateScenario = (swr: number, returnRate: number): ScenarioResult => {
    if (!fireData) {
      return {
        swr, returnRate, requiredCapital: 0, yearsToFire: 0, 
        fireDate: 0, ageAtFire: 0, monthlyInvestmentNeeded: 0, status: 'delayed'
      };
    }

    // Capital requis basé sur le SWR
    const requiredCapital = ANNUAL_GROSS_WITHDRAWAL / (swr / 100);
    
    // Montant restant à investir
    const remainingToInvest = Math.max(0, requiredCapital - fireData.current_net_patrimoine);
    
    // Calcul des années pour atteindre l'objectif
    let yearsToFire = 0;
    if (remainingToInvest > 0) {
      const monthlyReturn = returnRate / 100 / 12;
      const currentValue = fireData.current_net_patrimoine;
      const monthlyInvestment = MONTHLY_INVESTMENT_CAPACITY;
      
      // Formule pour calculer le temps nécessaire avec investissements mensuels et croissance
      if (monthlyReturn > 0) {
        const futureValueFactor = requiredCapital / currentValue;
        const paymentFactor = (monthlyInvestment * 12) / (currentValue * (returnRate / 100));
        
        if (futureValueFactor > 1) {
          yearsToFire = Math.log(1 + (futureValueFactor - 1) / paymentFactor) / Math.log(1 + returnRate / 100);
        } else {
          yearsToFire = 0;
        }
      } else {
        yearsToFire = remainingToInvest / (monthlyInvestment * 12);
      }
    }

    const fireDate = CURRENT_YEAR + yearsToFire;
    const ageAtFire = (CURRENT_YEAR - BIRTH_YEAR) + yearsToFire;
    
    // Investissement mensuel nécessaire pour atteindre l'objectif en 2038
    const yearsToTarget = TARGET_YEAR - CURRENT_YEAR;
    const monthlyReturn = returnRate / 100 / 12;
    let monthlyInvestmentNeeded = 0;
    
    if (yearsToTarget > 0 && monthlyReturn > 0) {
      const futureValueCurrent = fireData.current_net_patrimoine * Math.pow(1 + returnRate / 100, yearsToTarget);
      const remainingNeeded = Math.max(0, requiredCapital - futureValueCurrent);
      
      if (remainingNeeded > 0) {
        const monthsToTarget = yearsToTarget * 12;
        const futureValueFactor = Math.pow(1 + monthlyReturn, monthsToTarget) - 1;
        monthlyInvestmentNeeded = (remainingNeeded * monthlyReturn) / futureValueFactor;
      }
    }

    // Statut par rapport à l'objectif 2038
    let status: 'early' | 'ontime' | 'delayed' = 'ontime';
    if (fireDate < TARGET_YEAR - 0.5) status = 'early';
    else if (fireDate > TARGET_YEAR + 0.5) status = 'delayed';

    return {
      swr,
      returnRate,
      requiredCapital,
      yearsToFire,
      fireDate,
      ageAtFire,
      monthlyInvestmentNeeded,
      status
    };
  };

  const calculateCustomScenario = (): ScenarioResult => {
    return calculateScenario(customSwr, customReturn);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(value);
  };

  const formatYear = (year: number) => {
    return year.toFixed(1);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'early': return 'success';
      case 'ontime': return 'primary';
      case 'delayed': return 'danger';
      default: return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'early': return 'En avance';
      case 'ontime': return 'Dans les temps';
      case 'delayed': return 'En retard';
      default: return 'Inconnu';
    }
  };

  if (loading) return <p>Chargement...</p>;
  if (!fireData) return <p>Erreur lors du chargement des données FIRE.</p>;

  // Calcul de tous les scénarios
  const allScenarios: ScenarioResult[] = [];
  swrScenarios.forEach(swr => {
    returnScenarios.forEach(ret => {
      allScenarios.push(calculateScenario(swr.rate, ret.rate));
    });
  });

  const customScenario = calculateCustomScenario();
  const bestScenario = allScenarios.reduce((best, current) => 
    current.fireDate < best.fireDate ? current : best
  );
  const worstScenario = allScenarios.reduce((worst, current) => 
    current.fireDate > worst.fireDate ? current : worst
  );

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>🎯 Calculateur de Scénarios FIRE</h1>
      </div>

      {/* Résumé des scénarios */}
      <Row className="mb-4">
        <Col md={4}>
          <Card className="text-white bg-success">
            <Card.Body className="text-center">
              <h5>Meilleur Scénario</h5>
              <h4>{formatYear(bestScenario.fireDate)}</h4>
              <small>SWR {bestScenario.swr}% • Rendement {bestScenario.returnRate}%</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="text-white bg-primary">
            <Card.Body className="text-center">
              <h5>Objectif Documenté</h5>
              <h4>2038</h4>
              <small>SWR 4% • Rendement 5% • Âge 60 ans</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="text-white bg-danger">
            <Card.Body className="text-center">
              <h5>Pire Scénario</h5>
              <h4>{formatYear(worstScenario.fireDate)}</h4>
              <small>SWR {worstScenario.swr}% • Rendement {worstScenario.returnRate}%</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Informations de base */}
      <Alert variant="info" className="mb-4">
        <Alert.Heading>Paramètres de Base</Alert.Heading>
        <Row>
          <Col md={3}>
            <strong>Patrimoine actuel :</strong><br />
            {formatCurrency(fireData.current_net_patrimoine)}
          </Col>
          <Col md={3}>
            <strong>Retrait annuel brut :</strong><br />
            {formatCurrency(ANNUAL_GROSS_WITHDRAWAL)}
          </Col>
          <Col md={3}>
            <strong>Investissement mensuel :</strong><br />
            {formatCurrency(MONTHLY_INVESTMENT_CAPACITY)}
          </Col>
          <Col md={3}>
            <strong>Objectif documenté :</strong><br />
            2038 (âge 60 ans)
          </Col>
        </Row>
      </Alert>

      {/* Calculateur personnalisé */}
      <Card className="mb-4">
        <Card.Header>
          <h5>🔧 Calculateur Personnalisé</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Taux de Retrait Sécurisé (SWR) : {customSwr}%</Form.Label>
                <Form.Range
                  min={3}
                  max={6}
                  step={0.1}
                  value={customSwr}
                  onChange={(e) => setCustomSwr(parseFloat(e.target.value))}
                />
                <div className="d-flex justify-content-between">
                  <small>3% (Très conservateur)</small>
                  <small>6% (Très agressif)</small>
                </div>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Rendement Annuel Attendu : {customReturn}%</Form.Label>
                <Form.Range
                  min={3}
                  max={8}
                  step={0.1}
                  value={customReturn}
                  onChange={(e) => setCustomReturn(parseFloat(e.target.value))}
                />
                <div className="d-flex justify-content-between">
                  <small>3% (Très conservateur)</small>
                  <small>8% (Très optimiste)</small>
                </div>
              </Form.Group>
            </Col>
          </Row>
          
          <Row>
            <Col md={3}>
              <div className="text-center">
                <h6>Capital Requis</h6>
                <h4 className="text-primary">{formatCurrency(customScenario.requiredCapital)}</h4>
              </div>
            </Col>
            <Col md={3}>
              <div className="text-center">
                <h6>Date FIRE</h6>
                <h4 className="text-success">{formatYear(customScenario.fireDate)}</h4>
              </div>
            </Col>
            <Col md={3}>
              <div className="text-center">
                <h6>Âge FIRE</h6>
                <h4 className="text-info">{customScenario.ageAtFire.toFixed(1)} ans</h4>
              </div>
            </Col>
            <Col md={3}>
              <div className="text-center">
                <h6>Statut vs 2038</h6>
                <Badge bg={getStatusColor(customScenario.status)} className="fs-6">
                  {getStatusText(customScenario.status)}
                </Badge>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Matrice des scénarios */}
      <Card className="mb-4">
        <Card.Header>
          <h5>📊 Matrice des Scénarios (Date FIRE)</h5>
        </Card.Header>
        <Card.Body>
          <Table responsive bordered className="text-center">
            <thead>
              <tr>
                <th>SWR \ Rendement</th>
                {returnScenarios.map(ret => (
                  <th key={ret.rate} className="bg-light">
                    {ret.name}<br />
                    <small>{ret.rate}%</small>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {swrScenarios.map(swr => (
                <tr key={swr.rate}>
                  <td className="bg-light">
                    <strong>{swr.name}</strong><br />
                    <small>{swr.rate}%</small>
                  </td>
                  {returnScenarios.map(ret => {
                    const scenario = allScenarios.find(s => s.swr === swr.rate && s.returnRate === ret.rate);
                    return (
                      <td key={`${swr.rate}-${ret.rate}`} className={`table-${getStatusColor(scenario?.status || 'secondary')}`}>
                        <strong>{formatYear(scenario?.fireDate || 0)}</strong><br />
                        <small>{scenario?.ageAtFire.toFixed(1)} ans</small>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </Table>
          <div className="mt-2">
            <Badge bg="success" className="me-2">En avance (avant 2037.5)</Badge>
            <Badge bg="primary" className="me-2">Dans les temps (2037.5-2038.5)</Badge>
            <Badge bg="danger">En retard (après 2038.5)</Badge>
          </div>
        </Card.Body>
      </Card>

      {/* Tableau détaillé des scénarios */}
      <Card>
        <Card.Header>
          <h5>📋 Analyse Détaillée des Scénarios</h5>
        </Card.Header>
        <Card.Body>
          <Table responsive striped hover>
            <thead>
              <tr>
                <th>SWR</th>
                <th>Rendement</th>
                <th>Capital Requis</th>
                <th>Date FIRE</th>
                <th>Âge FIRE</th>
                <th>Années Restantes</th>
                <th>Investissement Mensuel pour 2038</th>
                <th>Statut</th>
              </tr>
            </thead>
            <tbody>
              {allScenarios
                .sort((a, b) => a.fireDate - b.fireDate)
                .map((scenario, index) => (
                <tr key={index}>
                  <td>{scenario.swr}%</td>
                  <td>{scenario.returnRate}%</td>
                  <td>{formatCurrency(scenario.requiredCapital)}</td>
                  <td><strong>{formatYear(scenario.fireDate)}</strong></td>
                  <td>{scenario.ageAtFire.toFixed(1)} ans</td>
                  <td>{scenario.yearsToFire.toFixed(1)} ans</td>
                  <td>
                    {scenario.monthlyInvestmentNeeded > 0 ?
                      formatCurrency(scenario.monthlyInvestmentNeeded) :
                      <span className="text-success">Objectif déjà atteignable</span>
                    }
                  </td>
                  <td>
                    <Badge bg={getStatusColor(scenario.status)}>
                      {getStatusText(scenario.status)}
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      {/* Analyse et recommandations */}
      <Alert variant="warning" className="mt-4">
        <Alert.Heading>💡 Analyse et Recommandations</Alert.Heading>
        <Row>
          <Col md={6}>
            <h6>Observations clés :</h6>
            <ul>
              <li>
                <strong>Scénario actuel (4% SWR, 5% rendement) :</strong> FIRE en {formatYear(allScenarios.find(s => s.swr === 4.0 && s.returnRate === 5.0)?.fireDate || 2038)}
              </li>
              <li>
                <strong>Meilleur cas :</strong> FIRE dès {formatYear(bestScenario.fireDate)} avec SWR {bestScenario.swr}% et rendement {bestScenario.returnRate}%
              </li>
              <li>
                <strong>Pire cas :</strong> FIRE retardé à {formatYear(worstScenario.fireDate)} avec SWR {worstScenario.swr}% et rendement {worstScenario.returnRate}%
              </li>
            </ul>
          </Col>
          <Col md={6}>
            <h6>Stratégies d'optimisation :</h6>
            <ul>
              <li>
                <strong>Pour accélérer :</strong> Viser un rendement de 6% ou accepter un SWR de 4.5%
              </li>
              <li>
                <strong>Pour sécuriser :</strong> Prévoir un SWR conservateur de 3.8% (capital requis : {formatCurrency(ANNUAL_GROSS_WITHDRAWAL / 0.038)})
              </li>
              <li>
                <strong>Flexibilité :</strong> Ajuster l'allocation d'actifs selon les conditions de marché
              </li>
            </ul>
          </Col>
        </Row>
      </Alert>
    </div>
  );
};

export default ScenariosFire;
