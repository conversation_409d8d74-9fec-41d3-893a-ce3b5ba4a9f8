{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './App.css';\nimport Dashboard from './components/Dashboard';\nimport Assets from './components/Assets';\nimport Liabilities from './components/Liabilities';\nimport FireTarget from './components/FireTarget';\nimport ApiTest from './components/ApiTest';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [view, setView] = useState('dashboard');\n  const renderView = () => {\n    switch (view) {\n      case 'assets':\n        return /*#__PURE__*/_jsxDEV(Assets, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 16\n        }, this);\n      case 'liabilities':\n        return /*#__PURE__*/_jsxDEV(Liabilities, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      case 'fire':\n        return /*#__PURE__*/_jsxDEV(FireTarget, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'test':\n        return /*#__PURE__*/_jsxDEV(ApiTest, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar navbar-expand-lg navbar-dark bg-dark\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-fluid\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          className: \"navbar-brand\",\n          href: \"#\",\n          onClick: () => setView('dashboard'),\n          children: \"FIRE Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"collapse navbar-collapse\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"navbar-nav me-auto mb-2 mb-lg-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                onClick: () => setView('dashboard'),\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                onClick: () => setView('assets'),\n                children: \"Patrimoine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                onClick: () => setView('liabilities'),\n                children: \"Emprunts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                className: \"nav-link\",\n                href: \"#\",\n                onClick: () => setView('fire'),\n                children: \"Objectif FIRE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container mt-4\",\n      children: renderView()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qcqo4iJBvceAiVI0/4q2D8KbFew=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Dashboard", "Assets", "Liabilities", "FireTarget", "ApiTest", "jsxDEV", "_jsxDEV", "App", "_s", "view", "<PERSON><PERSON><PERSON><PERSON>", "render<PERSON>iew", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "href", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './App.css';\nimport Dashboard from './components/Dashboard';\nimport Assets from './components/Assets';\nimport Liabilities from './components/Liabilities';\nimport FireTarget from './components/FireTarget';\nimport ApiTest from './components/ApiTest';\n\ntype View = 'dashboard' | 'assets' | 'liabilities' | 'fire' | 'test';\n\nfunction App() {\n  const [view, setView] = useState<View>('dashboard');\n\n  const renderView = () => {\n    switch (view) {\n      case 'assets':\n        return <Assets />;\n      case 'liabilities':\n        return <Liabilities />;\n      case 'fire':\n        return <FireTarget />;\n      case 'test':\n        return <ApiTest />;\n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <div className=\"App\">\n      <nav className=\"navbar navbar-expand-lg navbar-dark bg-dark\">\n        <div className=\"container-fluid\">\n          <a className=\"navbar-brand\" href=\"#\" onClick={() => setView('dashboard')}>FIRE Dashboard</a>\n          <div className=\"collapse navbar-collapse\">\n            <ul className=\"navbar-nav me-auto mb-2 mb-lg-0\">\n              <li className=\"nav-item\">\n                <a className=\"nav-link\" href=\"#\" onClick={() => setView('dashboard')}>Dashboard</a>\n              </li>\n              <li className=\"nav-item\">\n                <a className=\"nav-link\" href=\"#\" onClick={() => setView('assets')}>Patrimoine</a>\n              </li>\n              <li className=\"nav-item\">\n                <a className=\"nav-link\" href=\"#\" onClick={() => setView('liabilities')}>Emprunts</a>\n              </li>\n              <li className=\"nav-item\">\n                <a className=\"nav-link\" href=\"#\" onClick={() => setView('fire')}>Objectif FIRE</a>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </nav>\n      <main className=\"container mt-4\">\n        {renderView()}\n      </main>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,WAAW;AAClB,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI3C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAO,WAAW,CAAC;EAEnD,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQF,IAAI;MACV,KAAK,QAAQ;QACX,oBAAOH,OAAA,CAACL,MAAM;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnB,KAAK,aAAa;QAChB,oBAAOT,OAAA,CAACJ,WAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxB,KAAK,MAAM;QACT,oBAAOT,OAAA,CAACH,UAAU;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvB,KAAK,MAAM;QACT,oBAAOT,OAAA,CAACF,OAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpB;QACE,oBAAOT,OAAA,CAACN,SAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACET,OAAA;IAAKU,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBX,OAAA;MAAKU,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DX,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BX,OAAA;UAAGU,SAAS,EAAC,cAAc;UAACE,IAAI,EAAC,GAAG;UAACC,OAAO,EAAEA,CAAA,KAAMT,OAAO,CAAC,WAAW,CAAE;UAAAO,QAAA,EAAC;QAAc;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5FT,OAAA;UAAKU,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCX,OAAA;YAAIU,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC7CX,OAAA;cAAIU,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBX,OAAA;gBAAGU,SAAS,EAAC,UAAU;gBAACE,IAAI,EAAC,GAAG;gBAACC,OAAO,EAAEA,CAAA,KAAMT,OAAO,CAAC,WAAW,CAAE;gBAAAO,QAAA,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eACLT,OAAA;cAAIU,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBX,OAAA;gBAAGU,SAAS,EAAC,UAAU;gBAACE,IAAI,EAAC,GAAG;gBAACC,OAAO,EAAEA,CAAA,KAAMT,OAAO,CAAC,QAAQ,CAAE;gBAAAO,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACLT,OAAA;cAAIU,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBX,OAAA;gBAAGU,SAAS,EAAC,UAAU;gBAACE,IAAI,EAAC,GAAG;gBAACC,OAAO,EAAEA,CAAA,KAAMT,OAAO,CAAC,aAAa,CAAE;gBAAAO,QAAA,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACLT,OAAA;cAAIU,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBX,OAAA;gBAAGU,SAAS,EAAC,UAAU;gBAACE,IAAI,EAAC,GAAG;gBAACC,OAAO,EAAEA,CAAA,KAAMT,OAAO,CAAC,MAAM,CAAE;gBAAAO,QAAA,EAAC;cAAa;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNT,OAAA;MAAMU,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC7BN,UAAU,CAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACP,EAAA,CA9CQD,GAAG;AAAAa,EAAA,GAAHb,GAAG;AAgDZ,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}