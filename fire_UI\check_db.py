import sqlite3

# Connect to the database
conn = sqlite3.connect('backend/patrimoine.db')
cursor = conn.cursor()

# Check tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print('Tables:', tables)

# Check assets count
try:
    cursor.execute('SELECT COUNT(*) FROM assets')
    assets_count = cursor.fetchone()[0]
    print('Assets count:', assets_count)
except Exception as e:
    print('Error checking assets:', e)

# Check categories count
try:
    cursor.execute('SELECT COUNT(*) FROM categories')
    categories_count = cursor.fetchone()[0]
    print('Categories count:', categories_count)
except Exception as e:
    print('Error checking categories:', e)

# Check asset_category count
try:
    cursor.execute('SELECT COUNT(*) FROM asset_category')
    asset_category_count = cursor.fetchone()[0]
    print('Asset_category count:', asset_category_count)
except Exception as e:
    print('Error checking asset_category:', e)

conn.close()
