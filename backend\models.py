from sqlalchemy import Column, Integer, String, Float, Date, ForeignKey
from sqlalchemy.orm import relationship
from database import Base

class AssetCategory(Base):
    __tablename__ = 'asset_category'
    asset_id = Column(Integer, ForeignKey('assets.id'), primary_key=True)
    category_id = Column(Integer, ForeignKey('categories.id'), primary_key=True)
    value = Column(Float)
    asset = relationship("Asset", back_populates="categories")
    category = relationship("Category", back_populates="assets")

class Category(Base):
    __tablename__ = "categories"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    assets = relationship("AssetCategory", back_populates="category")

class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    value = Column(Float)
    annual_interest = Column(Float, nullable=True)
    notes = Column(String, nullable=True)
    update_date = Column(Date)
    categories = relationship("AssetCategory", back_populates="asset")
    liabilities = relationship("Liability", back_populates="asset")

class Liability(Base):
    __tablename__ = "liabilities"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    initial_amount = Column(Float)
    remaining_capital = Column(Float)
    interest_rate = Column(Float)
    end_date = Column(Date)
    monthly_payment = Column(Float)
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=True)
    asset = relationship("Asset", back_populates="liabilities")

class PatrimoineHistory(Base):
    __tablename__ = "patrimoine_history"

    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, unique=True)
    net_patrimoine = Column(Float)

class SCPI(Base):
    __tablename__ = "scpi"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    price_per_share = Column(Float)  # Prix par part
    number_of_shares = Column(Integer)  # Nombre de parts
    total_value = Column(Float)  # Valeur totale
    update_date = Column(Date)

class FireSettings(Base):
    __tablename__ = "fire_settings"

    id = Column(Integer, primary_key=True, index=True)
    fire_target_amount = Column(Float)  # Montant cible FIRE
    secure_withdrawal_rate = Column(Float)  # Taux de retrait sécurisé (SWR)
    update_date = Column(Date)
