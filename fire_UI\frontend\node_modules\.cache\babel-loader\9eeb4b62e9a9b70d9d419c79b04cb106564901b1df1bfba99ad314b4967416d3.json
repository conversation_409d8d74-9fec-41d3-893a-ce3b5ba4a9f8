{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\ApiTest.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport apiClient from './ApiClient';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ApiTest = () => {\n  _s();\n  const [result, setResult] = useState('');\n  const [loading, setLoading] = useState(false);\n  const testDashboard = async () => {\n    setLoading(true);\n    setResult('Testing...');\n    try {\n      const response = await apiClient.get('/dashboard');\n      setResult(`Success: ${JSON.stringify(response.data, null, 2)}`);\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$config, _error$config2, _error$config3;\n      setResult(`Error: ${error.message}\\nDetails: ${JSON.stringify({\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        config: {\n          url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n          baseURL: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.baseURL,\n          method: (_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.method\n        }\n      }, null, 2)}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testDirectFetch = async () => {\n    setLoading(true);\n    setResult('Testing with fetch...');\n    try {\n      const response = await fetch('http://localhost:8000/api/dashboard');\n      const data = await response.json();\n      setResult(`Fetch Success: ${JSON.stringify(data, null, 2)}`);\n    } catch (error) {\n      setResult(`Fetch Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testDirectFetch127 = async () => {\n    setLoading(true);\n    setResult('Testing with fetch (127.0.0.1)...');\n    try {\n      const response = await fetch('http://127.0.0.1:8000/api/dashboard');\n      const data = await response.json();\n      setResult(`Fetch Success (127.0.0.1): ${JSON.stringify(data, null, 2)}`);\n    } catch (error) {\n      setResult(`Fetch Error (127.0.0.1): ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"API Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary me-2\",\n        onClick: testDashboard,\n        disabled: loading,\n        children: \"Test Dashboard API (Axios)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        onClick: testDirectFetch,\n        disabled: loading,\n        children: \"Test Dashboard API (Fetch)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n      className: \"bg-light p-3\",\n      style: {\n        whiteSpace: 'pre-wrap'\n      },\n      children: result\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(ApiTest, \"+f+5BVLsSkcBSMc6rpBNO90CVC0=\");\n_c = ApiTest;\nexport default ApiTest;\nvar _c;\n$RefreshReg$(_c, \"ApiTest\");", "map": {"version": 3, "names": ["React", "useState", "apiClient", "jsxDEV", "_jsxDEV", "ApiTest", "_s", "result", "setResult", "loading", "setLoading", "testDashboard", "response", "get", "JSON", "stringify", "data", "error", "_error$response", "_error$response2", "_error$response3", "_error$config", "_error$config2", "_error$config3", "message", "status", "statusText", "config", "url", "baseURL", "method", "testDirectFetch", "fetch", "json", "testDirectFetch127", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/ApiTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport apiClient from './ApiClient';\n\nconst ApiTest: React.FC = () => {\n  const [result, setResult] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n\n  const testDashboard = async () => {\n    setLoading(true);\n    setResult('Testing...');\n    \n    try {\n      const response = await apiClient.get('/dashboard');\n      setResult(`Success: ${JSON.stringify(response.data, null, 2)}`);\n    } catch (error: any) {\n      setResult(`Error: ${error.message}\\nDetails: ${JSON.stringify({\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        config: {\n          url: error.config?.url,\n          baseURL: error.config?.baseURL,\n          method: error.config?.method\n        }\n      }, null, 2)}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testDirectFetch = async () => {\n    setLoading(true);\n    setResult('Testing with fetch...');\n\n    try {\n      const response = await fetch('http://localhost:8000/api/dashboard');\n      const data = await response.json();\n      setResult(`Fetch Success: ${JSON.stringify(data, null, 2)}`);\n    } catch (error: any) {\n      setResult(`Fetch Error: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testDirectFetch127 = async () => {\n    setLoading(true);\n    setResult('Testing with fetch (127.0.0.1)...');\n\n    try {\n      const response = await fetch('http://127.0.0.1:8000/api/dashboard');\n      const data = await response.json();\n      setResult(`Fetch Success (127.0.0.1): ${JSON.stringify(data, null, 2)}`);\n    } catch (error: any) {\n      setResult(`Fetch Error (127.0.0.1): ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container mt-4\">\n      <h2>API Test</h2>\n      <div className=\"mb-3\">\n        <button \n          className=\"btn btn-primary me-2\" \n          onClick={testDashboard}\n          disabled={loading}\n        >\n          Test Dashboard API (Axios)\n        </button>\n        <button \n          className=\"btn btn-secondary\" \n          onClick={testDirectFetch}\n          disabled={loading}\n        >\n          Test Dashboard API (Fetch)\n        </button>\n      </div>\n      <pre className=\"bg-light p-3\" style={{ whiteSpace: 'pre-wrap' }}>\n        {result}\n      </pre>\n    </div>\n  );\n};\n\nexport default ApiTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGP,QAAQ,CAAS,EAAE,CAAC;EAChD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMU,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCD,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,YAAY,CAAC;IAEvB,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMV,SAAS,CAACW,GAAG,CAAC,YAAY,CAAC;MAClDL,SAAS,CAAC,YAAYM,IAAI,CAACC,SAAS,CAACH,QAAQ,CAACI,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACjE,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;MACnBf,SAAS,CAAC,UAAUS,KAAK,CAACO,OAAO,cAAcV,IAAI,CAACC,SAAS,CAAC;QAC5DU,MAAM,GAAAP,eAAA,GAAED,KAAK,CAACL,QAAQ,cAAAM,eAAA,uBAAdA,eAAA,CAAgBO,MAAM;QAC9BC,UAAU,GAAAP,gBAAA,GAAEF,KAAK,CAACL,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBO,UAAU;QACtCV,IAAI,GAAAI,gBAAA,GAAEH,KAAK,CAACL,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBJ,IAAI;QAC1BW,MAAM,EAAE;UACNC,GAAG,GAAAP,aAAA,GAAEJ,KAAK,CAACU,MAAM,cAAAN,aAAA,uBAAZA,aAAA,CAAcO,GAAG;UACtBC,OAAO,GAAAP,cAAA,GAAEL,KAAK,CAACU,MAAM,cAAAL,cAAA,uBAAZA,cAAA,CAAcO,OAAO;UAC9BC,MAAM,GAAAP,cAAA,GAAEN,KAAK,CAACU,MAAM,cAAAJ,cAAA,uBAAZA,cAAA,CAAcO;QACxB;MACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCrB,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,uBAAuB,CAAC;IAElC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMoB,KAAK,CAAC,qCAAqC,CAAC;MACnE,MAAMhB,IAAI,GAAG,MAAMJ,QAAQ,CAACqB,IAAI,CAAC,CAAC;MAClCzB,SAAS,CAAC,kBAAkBM,IAAI,CAACC,SAAS,CAACC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IAC9D,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBT,SAAS,CAAC,gBAAgBS,KAAK,CAACO,OAAO,EAAE,CAAC;IAC5C,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCxB,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,mCAAmC,CAAC;IAE9C,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMoB,KAAK,CAAC,qCAAqC,CAAC;MACnE,MAAMhB,IAAI,GAAG,MAAMJ,QAAQ,CAACqB,IAAI,CAAC,CAAC;MAClCzB,SAAS,CAAC,8BAA8BM,IAAI,CAACC,SAAS,CAACC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBT,SAAS,CAAC,4BAA4BS,KAAK,CAACO,OAAO,EAAE,CAAC;IACxD,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA;IAAK+B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhC,OAAA;MAAAgC,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjBpC,OAAA;MAAK+B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBhC,OAAA;QACE+B,SAAS,EAAC,sBAAsB;QAChCM,OAAO,EAAE9B,aAAc;QACvB+B,QAAQ,EAAEjC,OAAQ;QAAA2B,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpC,OAAA;QACE+B,SAAS,EAAC,mBAAmB;QAC7BM,OAAO,EAAEV,eAAgB;QACzBW,QAAQ,EAAEjC,OAAQ;QAAA2B,QAAA,EACnB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNpC,OAAA;MAAK+B,SAAS,EAAC,cAAc;MAACQ,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MAAAR,QAAA,EAC7D7B;IAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAjFID,OAAiB;AAAAwC,EAAA,GAAjBxC,OAAiB;AAmFvB,eAAeA,OAAO;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}