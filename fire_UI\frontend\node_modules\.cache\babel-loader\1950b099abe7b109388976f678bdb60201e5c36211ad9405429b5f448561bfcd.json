{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(ArcElement, Tooltip, Legend);\nconst Dashboard = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchDashboardData = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/dashboard').then(response => {\n      setData(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('Dashboard API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchDashboardData(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des données du dashboard.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchDashboardData,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n  if (!data) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Aucune donn\\xE9e disponible.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 21\n  }, this);\n  const chartData = {\n    labels: Object.keys(data.allocation),\n    datasets: [{\n      label: 'Répartition du portefeuille',\n      data: Object.values(data.allocation),\n      backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)'],\n      borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)'],\n      borderWidth: 1\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"mb-4\",\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-primary mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Patrimoine Net Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.net_patrimoine)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-success mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Actifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.total_assets)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card text-white bg-danger mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: \"Total Passifs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"card-title\",\n              children: new Intl.NumberFormat('fr-FR', {\n                style: 'currency',\n                currency: 'EUR'\n              }).format(data.total_liabilities)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"R\\xE9partition des Actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Doughnut, {\n          data: chartData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"OfDpPZA5krn7xMvjWiPniKd42GY=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Doughnut", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "retryCount", "setRetryCount", "fetchDashboardData", "get", "then", "response", "catch", "console", "prev", "setTimeout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "chartData", "labels", "Object", "keys", "allocation", "datasets", "label", "values", "backgroundColor", "borderColor", "borderWidth", "Intl", "NumberFormat", "style", "currency", "format", "net_patrimoine", "total_assets", "total_liabilities", "_c", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\n\nChartJS.register(ArcElement, Tooltip, Legend);\n\ninterface DashboardData {\n  net_patrimoine: number;\n  total_assets: number;\n  total_liabilities: number;\n  allocation: { [key: string]: number };\n}\n\nconst Dashboard: React.FC = () => {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchDashboardData = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/dashboard')\n      .then(response => {\n        setData(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('Dashboard API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchDashboardData(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des données du dashboard.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  if (loading) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchDashboardData}>\n        Réessayer\n      </button>\n    </div>\n  );\n  if (!data) return <p>Aucune donnée disponible.</p>;\n\n  const chartData = {\n    labels: Object.keys(data.allocation),\n    datasets: [\n      {\n        label: 'Répartition du portefeuille',\n        data: Object.values(data.allocation),\n        backgroundColor: [\n          'rgba(255, 99, 132, 0.7)',\n          'rgba(54, 162, 235, 0.7)',\n          'rgba(255, 206, 86, 0.7)',\n          'rgba(75, 192, 192, 0.7)',\n          'rgba(153, 102, 255, 0.7)',\n          'rgba(255, 159, 64, 0.7)',\n        ],\n        borderColor: [\n          'rgba(255, 99, 132, 1)',\n          'rgba(54, 162, 235, 1)',\n          'rgba(255, 206, 86, 1)',\n          'rgba(75, 192, 192, 1)',\n          'rgba(153, 102, 255, 1)',\n          'rgba(255, 159, 64, 1)',\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  return (\n    <div>\n      <h1 className=\"mb-4\">Dashboard</h1>\n      <div className=\"row\">\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-primary mb-3\">\n            <div className=\"card-header\">Patrimoine Net Total</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.net_patrimoine)}</h5>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-success mb-3\">\n            <div className=\"card-header\">Total Actifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_assets)}</h5>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"card text-white bg-danger mb-3\">\n            <div className=\"card-header\">Total Passifs</div>\n            <div className=\"card-body\">\n              <h5 className=\"card-title\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(data.total_liabilities)}</h5>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"row mt-4\">\n        <div className=\"col-md-6\">\n          <h2>Répartition des Actifs</h2>\n          <Doughnut data={chartData} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,KAAK,IAAIC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzEL,OAAO,CAACM,QAAQ,CAACL,UAAU,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAS7C,MAAMI,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMqB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEdjB,SAAS,CAACqB,GAAG,CAAC,YAAY,CAAC,CACxBC,IAAI,CAACC,QAAQ,IAAI;MAChBV,OAAO,CAACU,QAAQ,CAACX,IAAI,CAAC;MACtBG,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDK,KAAK,CAACR,KAAK,IAAI;MACdS,OAAO,CAACT,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C,IAAIE,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACO,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAMP,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC;MAC9C,CAAC,MAAM;QACLH,QAAQ,CAAC,0DAA0D,CAAC;QACpEF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACdsB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIN,OAAO,EAAE,oBAAON,OAAA;IAAAoB,QAAA,EAAG;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACxC,IAAIhB,KAAK,EAAE,oBACTR,OAAA;IAAAoB,QAAA,gBACEpB,OAAA;MAAGyB,SAAS,EAAC,aAAa;MAAAL,QAAA,EAAEZ;IAAK;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtCxB,OAAA;MAAQyB,SAAS,EAAC,iBAAiB;MAACC,OAAO,EAAEd,kBAAmB;MAAAQ,QAAA,EAAC;IAEjE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAER,IAAI,CAACpB,IAAI,EAAE,oBAAOJ,OAAA;IAAAoB,QAAA,EAAG;EAAyB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAElD,MAAMG,SAAS,GAAG;IAChBC,MAAM,EAAEC,MAAM,CAACC,IAAI,CAAC1B,IAAI,CAAC2B,UAAU,CAAC;IACpCC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,6BAA6B;MACpC7B,IAAI,EAAEyB,MAAM,CAACK,MAAM,CAAC9B,IAAI,CAAC2B,UAAU,CAAC;MACpCI,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,CAC1B;MACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,CACxB;MACDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,oBACErC,OAAA;IAAAoB,QAAA,gBACEpB,OAAA;MAAIyB,SAAS,EAAC,MAAM;MAAAL,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnCxB,OAAA;MAAKyB,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClBpB,OAAA;QAAKyB,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBpB,OAAA;UAAKyB,SAAS,EAAC,iCAAiC;UAAAL,QAAA,gBAC9CpB,OAAA;YAAKyB,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvDxB,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBpB,OAAA;cAAIyB,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIkB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAACtC,IAAI,CAACuC,cAAc;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxB,OAAA;QAAKyB,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBpB,OAAA;UAAKyB,SAAS,EAAC,iCAAiC;UAAAL,QAAA,gBAC9CpB,OAAA;YAAKyB,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CxB,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBpB,OAAA;cAAIyB,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIkB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAACtC,IAAI,CAACwC,YAAY;YAAC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxB,OAAA;QAAKyB,SAAS,EAAC,UAAU;QAAAL,QAAA,eACvBpB,OAAA;UAAKyB,SAAS,EAAC,gCAAgC;UAAAL,QAAA,gBAC7CpB,OAAA;YAAKyB,SAAS,EAAC,aAAa;YAAAL,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDxB,OAAA;YAAKyB,SAAS,EAAC,WAAW;YAAAL,QAAA,eACxBpB,OAAA;cAAIyB,SAAS,EAAC,YAAY;cAAAL,QAAA,EAAE,IAAIkB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC,CAAC,CAACC,MAAM,CAACtC,IAAI,CAACyC,iBAAiB;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxB,OAAA;MAAKyB,SAAS,EAAC,UAAU;MAAAL,QAAA,eACvBpB,OAAA;QAAKyB,SAAS,EAAC,UAAU;QAAAL,QAAA,gBACvBpB,OAAA;UAAAoB,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BxB,OAAA,CAACP,QAAQ;UAACW,IAAI,EAAEuB;QAAU;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA5GID,SAAmB;AAAA4C,EAAA,GAAnB5C,SAAmB;AA8GzB,eAAeA,SAAS;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}