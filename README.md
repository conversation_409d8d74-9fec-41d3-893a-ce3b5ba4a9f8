# 🔥 FIRE Dashboard

[![React](https://img.shields.io/badge/React-18.x-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.x-green.svg)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-green.svg)](https://www.python.org/)
[![SQLite](https://img.shields.io/badge/SQLite-3.x-lightgrey.svg)](https://www.sqlite.org/)

Une application web moderne pour le suivi et la planification de votre indépendance financière selon la méthode **FIRE** (Financial Independence, Retire Early).

## 📋 Description du Projet

FIRE Dashboard est une application complète de gestion de patrimoine et de suivi d'objectifs d'indépendance financière. Elle permet de :

- **Visualiser** votre patrimoine net en temps réel
- **Planifier** votre parcours vers l'indépendance financière
- **Suivre** vos investissements et leur répartition
- **Calculer** vos revenus passifs potentiels selon la règle des 4%
- **Gérer** tous vos actifs et passifs de manière centralisée

## ✨ Fonctionnalités Principales

### 📊 Dashboard Interactif
- Vue d'ensemble du patrimoine net total
- Graphiques de répartition des actifs par catégorie
- Calculs automatiques des métriques FIRE
- Progression visuelle vers l'objectif d'indépendance

### 💰 Gestion des Actifs
- Comptes bancaires (Livrets, PEA, Assurance-vie)
- Investissements boursiers
- Crypto-actifs
- Prêts participatifs (crowdlending)
- Catégorisation automatique des investissements

### 🏢 Section SCPI Dédiée
- Gestion spécialisée des SCPI (Sociétés Civiles de Placement Immobilier)
- Calcul automatique des valeurs (prix × nombre de parts)
- Intégration dans la catégorie "Immobilier"
- Suivi des performances par SCPI

### 💳 Gestion des Passifs
- Emprunts immobiliers
- Crédits en cours
- Calcul automatique du patrimoine net

### 🎯 Objectifs FIRE Configurables
- Définition d'objectifs personnalisés
- Taux de retrait sécurisé (SWR) modifiable
- Calcul des revenus passifs potentiels
- Estimation du temps restant avant l'indépendance

## 🏗️ Architecture Technique

### Frontend
- **React 18** avec **TypeScript**
- **Bootstrap 5** pour l'interface utilisateur
- **Chart.js** pour les graphiques
- **Axios** pour les appels API
- Architecture modulaire avec composants réutilisables

### Backend
- **FastAPI** pour l'API REST
- **SQLAlchemy** comme ORM
- **Pydantic** pour la validation des données
- **SQLite** comme base de données
- Architecture CRUD complète

### Base de Données
```
├── assets (actifs)
├── liabilities (passifs)
├── scpi (SCPI)
├── categories (catégories d'actifs)
├── asset_category (relations actifs-catégories)
├── fire_settings (paramètres FIRE)
└── patrimoine_history (historique)
```

## 🚀 Installation

### Prérequis
- **Node.js** 16+ et **npm**
- **Python** 3.8+
- **Git**

### 1. Cloner le Repository
```bash
git clone https://github.com/laurentvv/fire_UI.git
cd fire_UI
```

### 2. Installation du Backend
```bash
cd backend

# Créer un environnement virtuel
python -m venv .venv

# Activer l'environnement virtuel
# Windows :
.venv\Scripts\activate
# macOS/Linux :
source .venv/bin/activate

# Installer les dépendances
pip install -r requirements.txt

# Initialiser la base de données
python seed.py
```

### 3. Installation du Frontend
```bash
cd ../frontend

# Installer les dépendances
npm install
```

### 4. Lancement de l'Application

#### Option A : Lancement automatique (Windows)
```bash
# Depuis la racine du projet
./run.bat
```

#### Option B : Lancement manuel
```bash
# Terminal 1 - Backend
cd backend
.venv\Scripts\activate
uvicorn main:app --reload

# Terminal 2 - Frontend
cd frontend
npm start
```

L'application sera accessible à :
- **Frontend** : http://localhost:3000
- **Backend API** : http://localhost:8000
- **Documentation API** : http://localhost:8000/docs

## 📁 Structure du Projet

```
fire_UI/
├── backend/                 # API FastAPI
│   ├── main.py             # Point d'entrée de l'API
│   ├── models.py           # Modèles SQLAlchemy
│   ├── schemas.py          # Schémas Pydantic
│   ├── crud.py             # Opérations CRUD
│   ├── database.py         # Configuration base de données
│   ├── seed.py             # Données d'exemple
│   └── requirements.txt    # Dépendances Python
├── frontend/               # Application React
│   ├── src/
│   │   ├── components/     # Composants React
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Assets.tsx
│   │   │   ├── SCPI.tsx
│   │   │   ├── Liabilities.tsx
│   │   │   └── FireTarget.tsx
│   │   ├── App.tsx         # Composant principal
│   │   └── index.tsx       # Point d'entrée
│   ├── package.json        # Dépendances Node.js
│   └── public/             # Fichiers statiques
├── run.bat                 # Script de lancement Windows
└── README.md              # Documentation
```

## 📖 Guide d'Utilisation

### 1. Premier Lancement
1. **Accédez au Dashboard** : Vue d'ensemble de votre patrimoine
2. **Configurez vos objectifs FIRE** : Définissez votre montant cible et votre SWR
3. **Ajoutez vos actifs** : Comptes, investissements, crypto-actifs
4. **Ajoutez vos SCPI** : Gestion dédiée de vos investissements immobiliers
5. **Renseignez vos emprunts** : Pour un calcul précis du patrimoine net

### 2. Navigation
- **Dashboard** : Vue d'ensemble et graphiques
- **Patrimoine** : Gestion des actifs
- **SCPI** : Gestion spécialisée des SCPI
- **Emprunts** : Gestion des passifs
- **Objectif FIRE** : Suivi de progression et configuration

### 3. Fonctionnalités Avancées
- **Calcul automatique** : Le patrimoine net se met à jour en temps réel
- **Catégorisation intelligente** : Les actifs sont automatiquement classés
- **Graphiques interactifs** : Visualisation de la répartition des investissements
- **Objectifs personnalisables** : Modifiez vos cibles selon votre stratégie

## 📸 Captures d'Écran

### Dashboard Principal
*[Ajoutez ici une capture d'écran du dashboard avec les graphiques]*

### Gestion des Actifs
*[Ajoutez ici une capture d'écran de la page patrimoine]*

### Section SCPI
*[Ajoutez ici une capture d'écran de la gestion SCPI]*

### Objectifs FIRE
*[Ajoutez ici une capture d'écran de la page objectifs FIRE]*

## 🔧 Configuration Avancée

### Variables d'Environnement
Créez un fichier `.env` dans le dossier `backend/` :
```env
DATABASE_URL=sqlite:///./patrimoine.db
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### Personnalisation des Catégories
Les catégories d'actifs peuvent être personnalisées dans le fichier `backend/seed.py` :
- Liquidité
- Bourse
- Crypto-Actifs
- Fonds sécurisés
- Immobilier
- Prêts participatifs

## 🤝 Contribution

### Comment Contribuer
1. **Fork** le repository
2. **Créez** une branche pour votre fonctionnalité (`git checkout -b feature/nouvelle-fonctionnalite`)
3. **Committez** vos changements (`git commit -am 'Ajout d'une nouvelle fonctionnalité'`)
4. **Poussez** vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. **Créez** une Pull Request

### Standards de Code
- **Frontend** : Utilisez TypeScript et suivez les conventions React
- **Backend** : Respectez les standards PEP 8 pour Python
- **Tests** : Ajoutez des tests pour les nouvelles fonctionnalités
- **Documentation** : Documentez les nouvelles API et composants

### Idées d'Améliorations
- [ ] Graphiques de performance historique
- [ ] Import/Export de données
- [ ] Notifications et alertes
- [ ] Mode sombre
- [ ] Application mobile
- [ ] Intégration avec des APIs bancaires
- [ ] Calculs de simulation Monte Carlo
- [ ] Rapports PDF automatisés

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- **React** et **TypeScript** pour le frontend moderne
- **FastAPI** pour l'API performante
- **Chart.js** pour les visualisations
- **Bootstrap** pour l'interface utilisateur
- La communauté **FIRE** pour l'inspiration

## 📞 Support

Pour toute question ou problème :
- **Issues GitHub** : [Créer une issue](https://github.com/laurentvv/fire_UI/issues)
- **Discussions** : [Discussions GitHub](https://github.com/laurentvv/fire_UI/discussions)

---

**Développé avec ❤️ pour la communauté FIRE française** 🇫🇷
Suivi Patrimoine et Fire
