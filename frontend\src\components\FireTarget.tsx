import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { <PERSON><PERSON>, Button, Form, ProgressBar } from 'react-bootstrap';

interface FireTargetData {
  fire_target_amount: number;
  secure_withdrawal_rate: number;
  current_net_patrimoine: number;
  total_assets: number;
  total_liabilities: number;
  remaining_to_invest: number;
  potential_passive_income: number;
  progress_percentage: number;
}

interface FireSettings {
  id: number;
  fire_target_amount: number;
  secure_withdrawal_rate: number;
  update_date: string;
}

const FireSettingsForm: React.FC<{
  settings: { fire_target_amount: number; secure_withdrawal_rate: number } | null,
  onSave: () => void,
  onCancel: () => void
}> = ({ settings, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    fire_target_amount: settings?.fire_target_amount || 910150,
    secure_withdrawal_rate: (settings?.secure_withdrawal_rate || 0.04) * 100, // Convert to percentage for display
  });

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    const dataToSend = {
      fire_target_amount: formData.fire_target_amount,
      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100, // Convert back to decimal
    };

    console.log('Updating FIRE settings:', dataToSend);

    try {
      const response = await apiClient.put('/fire-settings', dataToSend);
      console.log('FIRE settings updated successfully:', response.data);
      onSave();
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde des paramètres FIRE", error);
      console.error("Error details:", error.response?.data);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Objectif FIRE (€)</Form.Label>
        <Form.Control
          type="number"
          step="1000"
          value={formData.fire_target_amount}
          onChange={(e) => setFormData({ ...formData, fire_target_amount: parseFloat(e.target.value) || 0 })}
          required
        />
        <Form.Text className="text-muted">
          Montant total que vous souhaitez atteindre pour votre indépendance financière
        </Form.Text>
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Taux de retrait sécurisé (%)</Form.Label>
        <Form.Control
          type="number"
          step="0.1"
          min="1"
          max="10"
          value={formData.secure_withdrawal_rate}
          onChange={(e) => setFormData({ ...formData, secure_withdrawal_rate: parseFloat(e.target.value) || 4 })}
          required
        />
        <Form.Text className="text-muted">
          Pourcentage de votre patrimoine que vous pouvez retirer annuellement (règle des 4% = 4.0)
        </Form.Text>
      </Form.Group>

      <div className="d-flex justify-content-end gap-2">
        <Button variant="secondary" onClick={onCancel}>Annuler</Button>
        <Button variant="primary" type="submit">Sauvegarder</Button>
      </div>
    </Form>
  );
};

const FireTarget: React.FC = () => {
  const [data, setData] = useState<FireTargetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [showModal, setShowModal] = useState(false);

  const fetchFireTargetData = () => {
    setLoading(true);
    setError(null);

    apiClient.get('/fire-target')
      .then(response => {
        setData(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('FireTarget API error:', error);

        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchFireTargetData(), 1000);
        } else {
          setError('Erreur lors de la récupération des données FIRE.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchFireTargetData();
  }, []);

  const handleSettingsSave = () => {
    setShowModal(false);
    fetchFireTargetData(); // Refresh data after settings update
  };

  if (loading) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchFireTargetData}>
        Réessayer
      </button>
    </div>
  );
  if (!data) return <p>Aucune donnée disponible.</p>;

  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  const formatPercentage = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 1 }).format(value / 100);

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Objectif FIRE</h1>
        <Button
          variant="primary"
          onClick={() => setShowModal(true)}
        >
          Modifier l'objectif
        </Button>
      </div>

      {/* Progression Card */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">Progression vers votre Objectif FIRE</h5>
              <div className="row mb-3">
                <div className="col-md-4">
                  <div className="text-center">
                    <h3 className="text-primary">{formatCurrency(data.current_net_patrimoine)}</h3>
                    <small className="text-muted">Patrimoine Net Actuel</small>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center">
                    <h3 className="text-success">{formatCurrency(data.fire_target_amount)}</h3>
                    <small className="text-muted">Objectif FIRE</small>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center">
                    <h3 className="text-info">{formatPercentage(data.progress_percentage)}</h3>
                    <small className="text-muted">Progression</small>
                  </div>
                </div>
              </div>

              <ProgressBar
                now={data.progress_percentage}
                label={`${data.progress_percentage.toFixed(1)}%`}
                style={{ height: '30px' }}
                className="mb-3"
              />

              <div className="row">
                <div className="col-md-6">
                  <p className="mb-1">
                    <strong>Montant restant à investir :</strong>
                  </p>
                  <p className="text-danger fs-5">
                    {formatCurrency(Math.max(0, data.remaining_to_invest))}
                  </p>
                </div>
                <div className="col-md-6">
                  <p className="mb-1">
                    <strong>Revenu passif annuel potentiel :</strong>
                  </p>
                  <p className="text-success fs-5">
                    {formatCurrency(data.potential_passive_income)}
                  </p>
                  <small className="text-muted">
                    Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la règle des {formatPercentage(data.secure_withdrawal_rate * 100)}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Details Cards */}
      <div className="row">
        <div className="col-md-4">
          <div className="card text-white bg-success mb-3">
            <div className="card-header">Total Actifs</div>
            <div className="card-body">
              <h5 className="card-title">{formatCurrency(data.total_assets)}</h5>
              <p className="card-text">Incluant les SCPI et tous vos investissements</p>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-danger mb-3">
            <div className="card-header">Total Passifs</div>
            <div className="card-body">
              <h5 className="card-title">{formatCurrency(data.total_liabilities)}</h5>
              <p className="card-text">Emprunts et dettes en cours</p>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-info mb-3">
            <div className="card-header">Taux de Retrait Sécurisé</div>
            <div className="card-body">
              <h5 className="card-title">{formatPercentage(data.secure_withdrawal_rate * 100)}</h5>
              <p className="card-text">Pourcentage de retrait annuel sécurisé</p>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Modifier les Paramètres FIRE</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <FireSettingsForm
            settings={data ? {
              fire_target_amount: data.fire_target_amount,
              secure_withdrawal_rate: data.secure_withdrawal_rate
            } : null}
            onSave={handleSettingsSave}
            onCancel={() => setShowModal(false)}
          />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default FireTarget;