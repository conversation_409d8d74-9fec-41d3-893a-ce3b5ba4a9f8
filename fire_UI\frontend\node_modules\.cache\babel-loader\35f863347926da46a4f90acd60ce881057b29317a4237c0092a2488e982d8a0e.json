{"ast": null, "code": "var _jsxFileName = \"D:\\\\GIT\\\\fire_UI\\\\frontend\\\\src\\\\components\\\\Assets.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AssetForm = ({\n  asset,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  // Convert API format to form format\n  const convertApiCategoriesToForm = apiCategories => {\n    if (!apiCategories || apiCategories.length === 0) {\n      return [{\n        name: 'Bourse',\n        value: 0\n      }];\n    }\n    return apiCategories.map(cat => ({\n      name: cat.category_name,\n      value: cat.value\n    }));\n  };\n  const [formData, setFormData] = useState({\n    name: (asset === null || asset === void 0 ? void 0 : asset.name) || '',\n    value: (asset === null || asset === void 0 ? void 0 : asset.value) || 0,\n    annual_interest: (asset === null || asset === void 0 ? void 0 : asset.annual_interest) || null,\n    notes: (asset === null || asset === void 0 ? void 0 : asset.notes) || '',\n    categories: convertApiCategoriesToForm(asset === null || asset === void 0 ? void 0 : asset.categories),\n    update_date: asset !== null && asset !== void 0 && asset.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n  });\n  const handleCategoryChange = (index, field, value) => {\n    const newCategories = [...formData.categories];\n    newCategories[index] = {\n      ...newCategories[index],\n      [field]: value\n    };\n    setFormData({\n      ...formData,\n      categories: newCategories\n    });\n  };\n  const addCategory = () => {\n    setFormData({\n      ...formData,\n      categories: [...formData.categories, {\n        name: 'Bourse',\n        value: 0\n      }]\n    });\n  };\n  const removeCategory = index => {\n    const newCategories = [...formData.categories];\n    newCategories.splice(index, 1);\n    setFormData({\n      ...formData,\n      categories: newCategories\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const method = asset !== null && asset !== void 0 && asset.id ? 'put' : 'post';\n    const url = asset !== null && asset !== void 0 && asset.id ? `/assets/${asset.id}` : '/assets';\n    console.log('Submitting asset:', {\n      method,\n      url,\n      formData\n    });\n    try {\n      const response = await apiClient[method](url, formData);\n      console.log('Asset saved successfully:', response.data);\n      onSave();\n    } catch (error) {\n      var _error$response;\n      console.error(\"Erreur lors de la sauvegarde de l'actif\", error);\n      console.error(\"Error details:\", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Nom\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        value: formData.name,\n        onChange: e => setFormData({\n          ...formData,\n          name: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Valeur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.value,\n        onChange: e => setFormData({\n          ...formData,\n          value: parseFloat(e.target.value)\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Int\\xE9r\\xEAt Annuel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"number\",\n        value: formData.annual_interest || '',\n        onChange: e => setFormData({\n          ...formData,\n          annual_interest: parseFloat(e.target.value) || null\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Notes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        value: formData.notes || '',\n        onChange: e => setFormData({\n          ...formData,\n          notes: e.target.value\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n      children: \"Cat\\xE9gories\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this), formData.categories.map((category, index) => /*#__PURE__*/_jsxDEV(InputGroup, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Select, {\n        value: category.name,\n        onChange: e => handleCategoryChange(index, 'name', e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Bourse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Immobilier\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Crypto-actifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Pr\\xEAts Participatifs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Fonds S\\xE9curis\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Liquidit\\xE9s\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        type: \"number\",\n        value: category.value,\n        onChange: e => handleCategoryChange(index, 'value', parseFloat(e.target.value))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-danger\",\n        onClick: () => removeCategory(index),\n        children: \"X\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 21\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 17\n    }, this)), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outline-primary\",\n      onClick: addCategory,\n      children: \"Ajouter une cat\\xE9gorie\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: \"Date de mise \\xE0 jour\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"date\",\n        value: formData.update_date,\n        onChange: e => setFormData({\n          ...formData,\n          update_date: e.target.value\n        }),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"primary\",\n      type: \"submit\",\n      children: \"Sauvegarder\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"secondary\",\n      onClick: onCancel,\n      className: \"ms-2\",\n      children: \"Annuler\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 9\n  }, this);\n};\n_s(AssetForm, \"ezNVUFywVJThAVorqy4CWd/o4JM=\");\n_c = AssetForm;\nconst Assets = () => {\n  _s2();\n  const [assets, setAssets] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedAsset, setSelectedAsset] = useState(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const fetchAssets = () => {\n    setLoading(true);\n    setError(null);\n    apiClient.get('/assets').then(response => {\n      setAssets(response.data);\n      setLoading(false);\n      setRetryCount(0);\n    }).catch(error => {\n      console.error('Assets API error:', error);\n      if (retryCount < 2) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => fetchAssets(), 1000);\n      } else {\n        setError('Erreur lors de la récupération des actifs.');\n        setLoading(false);\n      }\n    });\n  };\n  useEffect(() => {\n    fetchAssets();\n  }, []);\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedAsset(null);\n    fetchAssets();\n  };\n  const handleDelete = async id => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet actif ?\")) {\n      try {\n        await apiClient.delete(`/assets/${id}`);\n        fetchAssets();\n      } catch (error) {\n        console.error(\"Erreur lors de la suppression de l'actif\", error);\n      }\n    }\n  };\n  if (loading && !showModal) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 37\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary\",\n      onClick: fetchAssets,\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Mon Patrimoine (Actifs)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => {\n          setSelectedAsset({});\n          setShowModal(true);\n        },\n        children: \"Ajouter un Actif\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table table-striped table-hover\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-dark\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Nom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Int\\xE9r\\xEAt Annuel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-end\",\n            children: \"Valeur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Derni\\xE8re M\\xE0J\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: assets.map(asset => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: asset.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: asset.categories.map(c => `${c.category_name} (${new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(c.value)})`).join(', ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: asset.annual_interest ? `${asset.annual_interest}%` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-end\",\n            children: new Intl.NumberFormat('fr-FR', {\n              style: 'currency',\n              currency: 'EUR'\n            }).format(asset.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: new Date(asset.update_date).toLocaleDateString('fr-FR')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              onClick: () => {\n                setSelectedAsset(asset);\n                setShowModal(true);\n              },\n              children: \"Modifier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), ' ', /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-danger\",\n              size: \"sm\",\n              onClick: () => handleDelete(asset.id),\n              children: \"Supprimer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)]\n        }, asset.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [selectedAsset !== null && selectedAsset !== void 0 && selectedAsset.id ? 'Modifier' : 'Ajouter', \" un Actif\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(AssetForm, {\n          asset: selectedAsset,\n          onSave: handleSave,\n          onCancel: () => setShowModal(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this);\n};\n_s2(Assets, \"eh7lWDQ0Yxh7bCt8el8bsd7dr44=\");\n_c2 = Assets;\nexport default Assets;\nvar _c, _c2;\n$RefreshReg$(_c, \"AssetForm\");\n$RefreshReg$(_c2, \"Assets\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "apiClient", "Modal", "<PERSON><PERSON>", "Form", "InputGroup", "FormControl", "jsxDEV", "_jsxDEV", "AssetForm", "asset", "onSave", "onCancel", "_s", "convertApiCategoriesToForm", "apiCategories", "length", "name", "value", "map", "cat", "category_name", "formData", "setFormData", "annual_interest", "notes", "categories", "update_date", "Date", "toISOString", "split", "handleCategoryChange", "index", "field", "newCategories", "addCategory", "removeCategory", "splice", "handleSubmit", "e", "preventDefault", "method", "id", "url", "console", "log", "response", "data", "error", "_error$response", "onSubmit", "children", "Group", "className", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "type", "onChange", "target", "required", "parseFloat", "category", "Select", "variant", "onClick", "_c", "Assets", "_s2", "assets", "setAssets", "loading", "setLoading", "setError", "showModal", "setShowModal", "selectedAsset", "setSelectedAsset", "retryCount", "setRetryCount", "fetchAssets", "get", "then", "catch", "prev", "setTimeout", "handleSave", "handleDelete", "window", "confirm", "delete", "c", "Intl", "NumberFormat", "style", "currency", "format", "join", "toLocaleDateString", "size", "show", "onHide", "Header", "closeButton", "Title", "Body", "_c2", "$RefreshReg$"], "sources": ["D:/GIT/fire_UI/frontend/src/components/Assets.tsx"], "sourcesContent": ["import React, { useEffect, useState, FormEvent } from 'react';\nimport apiClient from './ApiClient';\nimport { Modal, Button, Form, InputGroup, FormControl } from 'react-bootstrap';\n\ninterface CategoryValue {\n  name: string;\n  value: number;\n}\n\ninterface ApiCategoryValue {\n  category_name: string;\n  value: number;\n}\n\ninterface Asset {\n  id: number;\n  name: string;\n  value: number;\n  annual_interest: number | null;\n  notes: string | null;\n  update_date: string;\n  categories: ApiCategoryValue[];\n  liabilities: any[];\n}\n\nconst AssetForm: React.FC<{ asset: Partial<Asset> | null, onSave: () => void, onCancel: () => void }> = ({ asset, onSave, onCancel }) => {\n    // Convert API format to form format\n    const convertApiCategoriesToForm = (apiCategories: ApiCategoryValue[] | undefined): CategoryValue[] => {\n        if (!apiCategories || apiCategories.length === 0) {\n            return [{ name: '<PERSON><PERSON><PERSON>', value: 0 }];\n        }\n        return apiCategories.map(cat => ({ name: cat.category_name, value: cat.value }));\n    };\n\n    const [formData, setFormData] = useState({\n        name: asset?.name || '',\n        value: asset?.value || 0,\n        annual_interest: asset?.annual_interest || null,\n        notes: asset?.notes || '',\n        categories: convertApiCategoriesToForm(asset?.categories),\n        update_date: asset?.update_date ? new Date(asset.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],\n    });\n\n    const handleCategoryChange = (index: number, field: string, value: any) => {\n        const newCategories = [...formData.categories];\n        newCategories[index] = { ...newCategories[index], [field]: value };\n        setFormData({ ...formData, categories: newCategories });\n    };\n\n    const addCategory = () => {\n        setFormData({ ...formData, categories: [...formData.categories, { name: 'Bourse', value: 0 }] });\n    };\n\n    const removeCategory = (index: number) => {\n        const newCategories = [...formData.categories];\n        newCategories.splice(index, 1);\n        setFormData({ ...formData, categories: newCategories });\n    };\n\n    const handleSubmit = async (e: FormEvent) => {\n        e.preventDefault();\n        const method = asset?.id ? 'put' : 'post';\n        const url = asset?.id ? `/assets/${asset.id}` : '/assets';\n\n        console.log('Submitting asset:', { method, url, formData });\n\n        try {\n            const response = await apiClient[method](url, formData);\n            console.log('Asset saved successfully:', response.data);\n            onSave();\n        } catch (error: any) {\n            console.error(\"Erreur lors de la sauvegarde de l'actif\", error);\n            console.error(\"Error details:\", error.response?.data);\n        }\n    };\n\n    return (\n        <Form onSubmit={handleSubmit}>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Nom</Form.Label>\n                <Form.Control type=\"text\" value={formData.name} onChange={e => setFormData({ ...formData, name: e.target.value })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Valeur</Form.Label>\n                <Form.Control type=\"number\" value={formData.value} onChange={e => setFormData({ ...formData, value: parseFloat(e.target.value) })} required />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Intérêt Annuel</Form.Label>\n                <Form.Control type=\"number\" value={formData.annual_interest || ''} onChange={e => setFormData({ ...formData, annual_interest: parseFloat(e.target.value) || null })} />\n            </Form.Group>\n            <Form.Group className=\"mb-3\">\n                <Form.Label>Notes</Form.Label>\n                <Form.Control type=\"text\" value={formData.notes || ''} onChange={e => setFormData({ ...formData, notes: e.target.value })} />\n            </Form.Group>\n            \n            <h5>Catégories</h5>\n            {formData.categories.map((category, index) => (\n                <InputGroup className=\"mb-3\" key={index}>\n                    <Form.Select value={category.name} onChange={e => handleCategoryChange(index, 'name', e.target.value)}>\n                        <option>Bourse</option>\n                        <option>Immobilier</option>\n                        <option>Crypto-actifs</option>\n                        <option>Prêts Participatifs</option>\n                        <option>Fonds Sécurisés</option>\n                        <option>Liquidités</option>\n                    </Form.Select>\n                    <FormControl type=\"number\" value={category.value} onChange={e => handleCategoryChange(index, 'value', parseFloat(e.target.value))} />\n                    <Button variant=\"outline-danger\" onClick={() => removeCategory(index)}>X</Button>\n                </InputGroup>\n            ))}\n            <Button variant=\"outline-primary\" onClick={addCategory}>Ajouter une catégorie</Button>\n\n            <Form.Group className=\"mb-3 mt-3\">\n                <Form.Label>Date de mise à jour</Form.Label>\n                <Form.Control type=\"date\" value={formData.update_date} onChange={e => setFormData({ ...formData, update_date: e.target.value })} required />\n            </Form.Group>\n            <Button variant=\"primary\" type=\"submit\">Sauvegarder</Button>\n            <Button variant=\"secondary\" onClick={onCancel} className=\"ms-2\">Annuler</Button>\n        </Form>\n    );\n};\n\n\nconst Assets: React.FC = () => {\n  const [assets, setAssets] = useState<Asset[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedAsset, setSelectedAsset] = useState<Partial<Asset> | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n\n  const fetchAssets = () => {\n    setLoading(true);\n    setError(null);\n\n    apiClient.get('/assets')\n      .then(response => {\n        setAssets(response.data);\n        setLoading(false);\n        setRetryCount(0);\n      })\n      .catch(error => {\n        console.error('Assets API error:', error);\n\n        if (retryCount < 2) {\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => fetchAssets(), 1000);\n        } else {\n          setError('Erreur lors de la récupération des actifs.');\n          setLoading(false);\n        }\n      });\n  };\n\n  useEffect(() => {\n    fetchAssets();\n  }, []);\n\n  const handleSave = () => {\n    setShowModal(false);\n    setSelectedAsset(null);\n    fetchAssets();\n  };\n\n  const handleDelete = async (id: number) => {\n    if (window.confirm(\"Êtes-vous sûr de vouloir supprimer cet actif ?\")) {\n        try {\n            await apiClient.delete(`/assets/${id}`);\n            fetchAssets();\n        } catch (error) {\n            console.error(\"Erreur lors de la suppression de l'actif\", error);\n        }\n    }\n  };\n\n  if (loading && !showModal) return <p>Chargement...</p>;\n  if (error) return (\n    <div>\n      <p className=\"text-danger\">{error}</p>\n      <button className=\"btn btn-primary\" onClick={fetchAssets}>\n        Réessayer\n      </button>\n    </div>\n  );\n\n  return (\n    <div>\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\n        <h1>Mon Patrimoine (Actifs)</h1>\n        <Button variant=\"primary\" onClick={() => { setSelectedAsset({}); setShowModal(true); }}>Ajouter un Actif</Button>\n      </div>\n      <table className=\"table table-striped table-hover\">\n        <thead className=\"table-dark\">\n          <tr>\n            <th>Nom</th>\n            <th>Catégories</th>\n            <th className=\"text-end\">Intérêt Annuel</th>\n            <th className=\"text-end\">Valeur</th>\n            <th className=\"text-center\">Dernière MàJ</th>\n            <th className=\"text-center\">Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          {assets.map(asset => (\n            <tr key={asset.id}>\n              <td>{asset.name}</td>\n              <td>{asset.categories.map(c => `${c.category_name} (${new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(c.value)})`).join(', ')}</td>\n              <td className=\"text-end\">{asset.annual_interest ? `${asset.annual_interest}%` : 'N/A'}</td>\n              <td className=\"text-end\">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(asset.value)}</td>\n              <td className=\"text-center\">{new Date(asset.update_date).toLocaleDateString('fr-FR')}</td>\n              <td className=\"text-center\">\n                <Button variant=\"outline-primary\" size=\"sm\" onClick={() => { setSelectedAsset(asset); setShowModal(true); }}>Modifier</Button>{' '}\n                <Button variant=\"outline-danger\" size=\"sm\" onClick={() => handleDelete(asset.id)}>Supprimer</Button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n\n      <Modal show={showModal} onHide={() => setShowModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>{selectedAsset?.id ? 'Modifier' : 'Ajouter'} un Actif</Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <AssetForm asset={selectedAsset} onSave={handleSave} onCancel={() => setShowModal(false)} />\n        </Modal.Body>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Assets;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAC7D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuB/E,MAAMC,SAA+F,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrI;EACA,MAAMC,0BAA0B,GAAIC,aAA6C,IAAsB;IACnG,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO,CAAC;QAAEC,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;IACzC;IACA,OAAOH,aAAa,CAACI,GAAG,CAACC,GAAG,KAAK;MAAEH,IAAI,EAAEG,GAAG,CAACC,aAAa;MAAEH,KAAK,EAAEE,GAAG,CAACF;IAAM,CAAC,CAAC,CAAC;EACpF,CAAC;EAED,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACrCiB,IAAI,EAAE,CAAAP,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,IAAI,KAAI,EAAE;IACvBC,KAAK,EAAE,CAAAR,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,KAAK,KAAI,CAAC;IACxBM,eAAe,EAAE,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,eAAe,KAAI,IAAI;IAC/CC,KAAK,EAAE,CAAAf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,KAAK,KAAI,EAAE;IACzBC,UAAU,EAAEZ,0BAA0B,CAACJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgB,UAAU,CAAC;IACzDC,WAAW,EAAEjB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEiB,WAAW,GAAG,IAAIC,IAAI,CAAClB,KAAK,CAACiB,WAAW,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACrI,CAAC,CAAC;EAEF,MAAMC,oBAAoB,GAAGA,CAACC,KAAa,EAAEC,KAAa,EAAEf,KAAU,KAAK;IACvE,MAAMgB,aAAa,GAAG,CAAC,GAAGZ,QAAQ,CAACI,UAAU,CAAC;IAC9CQ,aAAa,CAACF,KAAK,CAAC,GAAG;MAAE,GAAGE,aAAa,CAACF,KAAK,CAAC;MAAE,CAACC,KAAK,GAAGf;IAAM,CAAC;IAClEK,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEI,UAAU,EAAEQ;IAAc,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtBZ,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEI,UAAU,EAAE,CAAC,GAAGJ,QAAQ,CAACI,UAAU,EAAE;QAAET,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAE,CAAC;IAAE,CAAC,CAAC;EACpG,CAAC;EAED,MAAMkB,cAAc,GAAIJ,KAAa,IAAK;IACtC,MAAME,aAAa,GAAG,CAAC,GAAGZ,QAAQ,CAACI,UAAU,CAAC;IAC9CQ,aAAa,CAACG,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;IAC9BT,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEI,UAAU,EAAEQ;IAAc,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOC,CAAY,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,MAAM,GAAG/B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEgC,EAAE,GAAG,KAAK,GAAG,MAAM;IACzC,MAAMC,GAAG,GAAGjC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEgC,EAAE,GAAG,WAAWhC,KAAK,CAACgC,EAAE,EAAE,GAAG,SAAS;IAEzDE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAAEJ,MAAM;MAAEE,GAAG;MAAErB;IAAS,CAAC,CAAC;IAE3D,IAAI;MACA,MAAMwB,QAAQ,GAAG,MAAM7C,SAAS,CAACwC,MAAM,CAAC,CAACE,GAAG,EAAErB,QAAQ,CAAC;MACvDsB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,QAAQ,CAACC,IAAI,CAAC;MACvDpC,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOqC,KAAU,EAAE;MAAA,IAAAC,eAAA;MACjBL,OAAO,CAACI,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DJ,OAAO,CAACI,KAAK,CAAC,gBAAgB,GAAAC,eAAA,GAAED,KAAK,CAACF,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,CAAC;IACzD;EACJ,CAAC;EAED,oBACIvC,OAAA,CAACJ,IAAI;IAAC8C,QAAQ,EAAEZ,YAAa;IAAAa,QAAA,gBACzB3C,OAAA,CAACJ,IAAI,CAACgD,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB3C,OAAA,CAACJ,IAAI,CAACkD,KAAK;QAAAH,QAAA,EAAC;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5BlD,OAAA,CAACJ,IAAI,CAACuD,OAAO;QAACC,IAAI,EAAC,MAAM;QAAC1C,KAAK,EAAEI,QAAQ,CAACL,IAAK;QAAC4C,QAAQ,EAAEtB,CAAC,IAAIhB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEL,IAAI,EAAEsB,CAAC,CAACuB,MAAM,CAAC5C;QAAM,CAAC,CAAE;QAAC6C,QAAQ;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtH,CAAC,eACblD,OAAA,CAACJ,IAAI,CAACgD,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB3C,OAAA,CAACJ,IAAI,CAACkD,KAAK;QAAAH,QAAA,EAAC;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC/BlD,OAAA,CAACJ,IAAI,CAACuD,OAAO;QAACC,IAAI,EAAC,QAAQ;QAAC1C,KAAK,EAAEI,QAAQ,CAACJ,KAAM;QAAC2C,QAAQ,EAAEtB,CAAC,IAAIhB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEJ,KAAK,EAAE8C,UAAU,CAACzB,CAAC,CAACuB,MAAM,CAAC5C,KAAK;QAAE,CAAC,CAAE;QAAC6C,QAAQ;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtI,CAAC,eACblD,OAAA,CAACJ,IAAI,CAACgD,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB3C,OAAA,CAACJ,IAAI,CAACkD,KAAK;QAAAH,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACvClD,OAAA,CAACJ,IAAI,CAACuD,OAAO;QAACC,IAAI,EAAC,QAAQ;QAAC1C,KAAK,EAAEI,QAAQ,CAACE,eAAe,IAAI,EAAG;QAACqC,QAAQ,EAAEtB,CAAC,IAAIhB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEE,eAAe,EAAEwC,UAAU,CAACzB,CAAC,CAACuB,MAAM,CAAC5C,KAAK,CAAC,IAAI;QAAK,CAAC;MAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/J,CAAC,eACblD,OAAA,CAACJ,IAAI,CAACgD,KAAK;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB3C,OAAA,CAACJ,IAAI,CAACkD,KAAK;QAAAH,QAAA,EAAC;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC9BlD,OAAA,CAACJ,IAAI,CAACuD,OAAO;QAACC,IAAI,EAAC,MAAM;QAAC1C,KAAK,EAAEI,QAAQ,CAACG,KAAK,IAAI,EAAG;QAACoC,QAAQ,EAAEtB,CAAC,IAAIhB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEG,KAAK,EAAEc,CAAC,CAACuB,MAAM,CAAC5C;QAAM,CAAC;MAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrH,CAAC,eAEblD,OAAA;MAAA2C,QAAA,EAAI;IAAU;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAClBpC,QAAQ,CAACI,UAAU,CAACP,GAAG,CAAC,CAAC8C,QAAQ,EAAEjC,KAAK,kBACrCxB,OAAA,CAACH,UAAU;MAACgD,SAAS,EAAC,MAAM;MAAAF,QAAA,gBACxB3C,OAAA,CAACJ,IAAI,CAAC8D,MAAM;QAAChD,KAAK,EAAE+C,QAAQ,CAAChD,IAAK;QAAC4C,QAAQ,EAAEtB,CAAC,IAAIR,oBAAoB,CAACC,KAAK,EAAE,MAAM,EAAEO,CAAC,CAACuB,MAAM,CAAC5C,KAAK,CAAE;QAAAiC,QAAA,gBAClG3C,OAAA;UAAA2C,QAAA,EAAQ;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvBlD,OAAA;UAAA2C,QAAA,EAAQ;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3BlD,OAAA;UAAA2C,QAAA,EAAQ;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9BlD,OAAA;UAAA2C,QAAA,EAAQ;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpClD,OAAA;UAAA2C,QAAA,EAAQ;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChClD,OAAA;UAAA2C,QAAA,EAAQ;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACdlD,OAAA,CAACF,WAAW;QAACsD,IAAI,EAAC,QAAQ;QAAC1C,KAAK,EAAE+C,QAAQ,CAAC/C,KAAM;QAAC2C,QAAQ,EAAEtB,CAAC,IAAIR,oBAAoB,CAACC,KAAK,EAAE,OAAO,EAAEgC,UAAU,CAACzB,CAAC,CAACuB,MAAM,CAAC5C,KAAK,CAAC;MAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrIlD,OAAA,CAACL,MAAM;QAACgE,OAAO,EAAC,gBAAgB;QAACC,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAACJ,KAAK,CAAE;QAAAmB,QAAA,EAAC;MAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,GAVnD1B,KAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAW3B,CACf,CAAC,eACFlD,OAAA,CAACL,MAAM;MAACgE,OAAO,EAAC,iBAAiB;MAACC,OAAO,EAAEjC,WAAY;MAAAgB,QAAA,EAAC;IAAqB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEtFlD,OAAA,CAACJ,IAAI,CAACgD,KAAK;MAACC,SAAS,EAAC,WAAW;MAAAF,QAAA,gBAC7B3C,OAAA,CAACJ,IAAI,CAACkD,KAAK;QAAAH,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5ClD,OAAA,CAACJ,IAAI,CAACuD,OAAO;QAACC,IAAI,EAAC,MAAM;QAAC1C,KAAK,EAAEI,QAAQ,CAACK,WAAY;QAACkC,QAAQ,EAAEtB,CAAC,IAAIhB,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEK,WAAW,EAAEY,CAAC,CAACuB,MAAM,CAAC5C;QAAM,CAAC,CAAE;QAAC6C,QAAQ;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpI,CAAC,eACblD,OAAA,CAACL,MAAM;MAACgE,OAAO,EAAC,SAAS;MAACP,IAAI,EAAC,QAAQ;MAAAT,QAAA,EAAC;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC5DlD,OAAA,CAACL,MAAM;MAACgE,OAAO,EAAC,WAAW;MAACC,OAAO,EAAExD,QAAS;MAACyC,SAAS,EAAC,MAAM;MAAAF,QAAA,EAAC;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9E,CAAC;AAEf,CAAC;AAAC7C,EAAA,CA/FIJ,SAA+F;AAAA4D,EAAA,GAA/F5D,SAA+F;AAkGrG,MAAM6D,MAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzE,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,KAAK,EAAE4B,QAAQ,CAAC,GAAG5E,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6E,SAAS,EAAEC,YAAY,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAwB,IAAI,CAAC;EAC/E,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMmF,WAAW,GAAGA,CAAA,KAAM;IACxBR,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEd3E,SAAS,CAACmF,GAAG,CAAC,SAAS,CAAC,CACrBC,IAAI,CAACvC,QAAQ,IAAI;MAChB2B,SAAS,CAAC3B,QAAQ,CAACC,IAAI,CAAC;MACxB4B,UAAU,CAAC,KAAK,CAAC;MACjBO,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CACDI,KAAK,CAACtC,KAAK,IAAI;MACdJ,OAAO,CAACI,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAEzC,IAAIiC,UAAU,GAAG,CAAC,EAAE;QAClBC,aAAa,CAACK,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAML,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;MACvC,CAAC,MAAM;QACLP,QAAQ,CAAC,4CAA4C,CAAC;QACtDD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACN,CAAC;EAED5E,SAAS,CAAC,MAAM;IACdoF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBX,YAAY,CAAC,KAAK,CAAC;IACnBE,gBAAgB,CAAC,IAAI,CAAC;IACtBG,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOhD,EAAU,IAAK;IACzC,IAAIiD,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAClE,IAAI;QACA,MAAM3F,SAAS,CAAC4F,MAAM,CAAC,WAAWnD,EAAE,EAAE,CAAC;QACvCyC,WAAW,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACZJ,OAAO,CAACI,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE;IACJ;EACF,CAAC;EAED,IAAI0B,OAAO,IAAI,CAACG,SAAS,EAAE,oBAAOrE,OAAA;IAAA2C,QAAA,EAAG;EAAa;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACtD,IAAIV,KAAK,EAAE,oBACTxC,OAAA;IAAA2C,QAAA,gBACE3C,OAAA;MAAG6C,SAAS,EAAC,aAAa;MAAAF,QAAA,EAAEH;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtClD,OAAA;MAAQ6C,SAAS,EAAC,iBAAiB;MAACe,OAAO,EAAEe,WAAY;MAAAhC,QAAA,EAAC;IAE1D;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;EAGR,oBACElD,OAAA;IAAA2C,QAAA,gBACE3C,OAAA;MAAK6C,SAAS,EAAC,wDAAwD;MAAAF,QAAA,gBACrE3C,OAAA;QAAA2C,QAAA,EAAI;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChClD,OAAA,CAACL,MAAM;QAACgE,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAM;UAAEY,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAAEF,YAAY,CAAC,IAAI,CAAC;QAAE,CAAE;QAAA3B,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9G,CAAC,eACNlD,OAAA;MAAO6C,SAAS,EAAC,iCAAiC;MAAAF,QAAA,gBAChD3C,OAAA;QAAO6C,SAAS,EAAC,YAAY;QAAAF,QAAA,eAC3B3C,OAAA;UAAA2C,QAAA,gBACE3C,OAAA;YAAA2C,QAAA,EAAI;UAAG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZlD,OAAA;YAAA2C,QAAA,EAAI;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBlD,OAAA;YAAI6C,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ClD,OAAA;YAAI6C,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpClD,OAAA;YAAI6C,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ClD,OAAA;YAAI6C,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRlD,OAAA;QAAA2C,QAAA,EACGqB,MAAM,CAACrD,GAAG,CAACT,KAAK,iBACfF,OAAA;UAAA2C,QAAA,gBACE3C,OAAA;YAAA2C,QAAA,EAAKzC,KAAK,CAACO;UAAI;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBlD,OAAA;YAAA2C,QAAA,EAAKzC,KAAK,CAACgB,UAAU,CAACP,GAAG,CAAC2E,CAAC,IAAI,GAAGA,CAAC,CAACzE,aAAa,KAAK,IAAI0E,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAACL,CAAC,CAAC5E,KAAK,CAAC,GAAG,CAAC,CAACkF,IAAI,CAAC,IAAI;UAAC;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjKlD,OAAA;YAAI6C,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAEzC,KAAK,CAACc,eAAe,GAAG,GAAGd,KAAK,CAACc,eAAe,GAAG,GAAG;UAAK;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3FlD,OAAA;YAAI6C,SAAS,EAAC,UAAU;YAAAF,QAAA,EAAE,IAAI4C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;cAAEC,KAAK,EAAE,UAAU;cAAEC,QAAQ,EAAE;YAAM,CAAC,CAAC,CAACC,MAAM,CAACzF,KAAK,CAACQ,KAAK;UAAC;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1HlD,OAAA;YAAI6C,SAAS,EAAC,aAAa;YAAAF,QAAA,EAAE,IAAIvB,IAAI,CAAClB,KAAK,CAACiB,WAAW,CAAC,CAAC0E,kBAAkB,CAAC,OAAO;UAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1FlD,OAAA;YAAI6C,SAAS,EAAC,aAAa;YAAAF,QAAA,gBACzB3C,OAAA,CAACL,MAAM;cAACgE,OAAO,EAAC,iBAAiB;cAACmC,IAAI,EAAC,IAAI;cAAClC,OAAO,EAAEA,CAAA,KAAM;gBAAEY,gBAAgB,CAACtE,KAAK,CAAC;gBAAEoE,YAAY,CAAC,IAAI,CAAC;cAAE,CAAE;cAAA3B,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAClIlD,OAAA,CAACL,MAAM;cAACgE,OAAO,EAAC,gBAAgB;cAACmC,IAAI,EAAC,IAAI;cAAClC,OAAO,EAAEA,CAAA,KAAMsB,YAAY,CAAChF,KAAK,CAACgC,EAAE,CAAE;cAAAS,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC;QAAA,GATEhD,KAAK,CAACgC,EAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUb,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAERlD,OAAA,CAACN,KAAK;MAACqG,IAAI,EAAE1B,SAAU;MAAC2B,MAAM,EAAEA,CAAA,KAAM1B,YAAY,CAAC,KAAK,CAAE;MAAA3B,QAAA,gBACxD3C,OAAA,CAACN,KAAK,CAACuG,MAAM;QAACC,WAAW;QAAAvD,QAAA,eACvB3C,OAAA,CAACN,KAAK,CAACyG,KAAK;UAAAxD,QAAA,GAAE4B,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAErC,EAAE,GAAG,UAAU,GAAG,SAAS,EAAC,WAAS;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACflD,OAAA,CAACN,KAAK,CAAC0G,IAAI;QAAAzD,QAAA,eACT3C,OAAA,CAACC,SAAS;UAACC,KAAK,EAAEqE,aAAc;UAACpE,MAAM,EAAE8E,UAAW;UAAC7E,QAAQ,EAAEA,CAAA,KAAMkE,YAAY,CAAC,KAAK;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACa,GAAA,CA1GID,MAAgB;AAAAuC,GAAA,GAAhBvC,MAAgB;AA4GtB,eAAeA,MAAM;AAAC,IAAAD,EAAA,EAAAwC,GAAA;AAAAC,YAAA,CAAAzC,EAAA;AAAAyC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}